@model WebApp.Models.BestPracticeViewModel;
@using Binit.Framework;
@using Domain.Entities.Model.Enum;
@using Microsoft.Extensions.Localization;
@using WebApp.WebTools;

@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.BestPractice.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer

@{
    var action = ViewData["Action"] as string;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var entityTypes = ViewData["EntityTypes"] as List<SelectListItem>;
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
    var relevances = ViewData["Relevances"] as List<SelectListItem>;
    var taskTypes = ViewData["TaskTypes"] as List<SelectListItem>;
    var timePeriodTypes = ViewData["SpanTimePeriodTypes"] as List<SelectListItem>;
    var weekDays = ViewData["WeekDays"] as List<SelectListItem>;
    var frequencyCheckList = ViewData["FrequencyCheckList"] as List<SelectListItem>;
    var farmUserRolesEnum = ViewData["FarmUserRolesEnum"] as List<SelectListItem>;

}

<form id="best-practice-form" class="floating-labels" method="POST" action="@action">
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="AreaEnum" />

    <div class="row align-items-end">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-input for-property="Name" class="sides-margin"></ignite-input>
        </div>

        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="EntityType"
                             items="@entityTypes"
                             placeholder="@localizer[Lang.PlaceholderSelectEntityType]">
            </ignite-dropdown>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-textarea for-property="Description"></ignite-textarea>
        </div>

        @if (Model.AreaEnum != "Maintenance") //Area can only be maintenance, healthcare or none. If no area is specified, i may also create a best practice for a genetic
        {
            <div id="geneticContainer"
                 class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12"
                 style="display:@(Model.EntityId == null || Model.EntityId == "" ? "none" : "")">
                <ignite-dropdown for-property="EntityId"
                                 items="@genetics"
                                 placeholder="@localizer[Lang.PlaceholderSelectGenetic]">
                </ignite-dropdown>
            </div>
        }
    </div>

    <div class="row align-items-center">
        <div class="col-md-2" id="automaticBool" style="display:@(Model.EntityType == EntityTypeEnum.Genetic.ToString() || Model.EntityType == EntityTypeEnum.HenBatch.ToString()? "":"none")">
            <ignite-checkbox for-property="IsAutomaticallyAssigned" color="#f07a22"></ignite-checkbox>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <button type="button" id="fileBtn" class="btn btn-success"><i class='fa fa-file'></i>@localizer[Lang.FileButton]</button>
        </div>
    </div>

    <div class="form-group m-b-20 col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" binit-validation-for="TaskValidationErrors"
         binit-onerror-class="has-danger" binit-onsuccess-class="has-success">
        <span asp-validation-for="TaskValidationErrors" class="form-control-feedback"></span>
    </div>

    <div id="tasksContainer">
        @{
            int jj = Model.BestPracticeTasks.Count();
            int i = 0;
            foreach (var bpt in Model.BestPracticeTasks)
            {
                @await Component.InvokeAsync("BestPracticeTaskRow", new { index = i, model = Model, relevances = relevances, taskTypes = taskTypes, weekDays = weekDays, frequencyCheckList = frequencyCheckList, farmUserRolesEnum = farmUserRolesEnum, timePeriodTypes = timePeriodTypes });
                i++;
            }
        }
    </div>

    <div>
        <button id="add-task" type="button" class="btn btn-themecolor mr-2 mb-2" style="margin-top:15px;justify-content:end; display:@(Model.EntityType == null ? " none" : "" )">
            <i class="fa fa-plus"></i> @(localizer[Lang.BtnAddTask])
        </button>
    </div>

    <div class="d-flex justify-content-end" style="margin-top: 25px">
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index","BestPractice", new { area=Model.AreaEnum })'">
            @localizer[Lang.BtnCancel]
        </button>
        <button type="submit" class="btn btn-themecolor">@submitLabel</button>
    </div>

</form>


<!-- BEGIN MODAL TO UPLOAD FILES -->
<div class="modal none-border" id="uploadBestPracticeFile">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div class="col-md-11 row">
                    <div class="col-md-10">
                        <h4 class="modal-title p-b-10"><strong>@localizer[Lang.FileButton]</strong></h4>
                    </div>
                </div>
                <button type="button" class="close col-md-2" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
            <div class="modal-body">
                <label asp-for="FilesIds"></label>
                @await Html.PartialAsync("_FileManager", Model.Files,
                 new FileManagerOptions(ViewData, localizer)
                 {
                     UploadEnabled = true,
                     MaxFiles = 3,
                     ParentFormId = "best-practice-form",
                     PropertyName = "FilesIds",
                 }
                  )
            </div>
        </div>
    </div>

</div>


<div id="modalContainer">
    @{
        int j = 0;
        foreach (var bpt in Model.BestPracticeTasks)
        {
            @await Component.InvokeAsync("ModalBestPracticeTask", new { index = j, model = Model });
            j++;
        }
    }
</div>
<!-- END MODAL -->
<ignite-load plugins="select2,dropzone,switchery"></ignite-load>
@section Scripts {
    <script>
        var bestPracticeCreateOrEditResources = @Json.Serialize(ViewData["BestPracticeCreateOrEditResources"]);
        var entityType = '@Model.EntityType';
    </script>
    <script src="@Url.Content("~/js/views/bestPractice/createOrEdit.js")" type="text/javascript"></script>
}