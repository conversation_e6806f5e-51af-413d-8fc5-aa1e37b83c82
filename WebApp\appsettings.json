{"DatabaseConfiguration": {"ModelProvider": "SqlServer", "ModelConnection": "Data Source=pm-dev-sqlsrv-br01.database.windows.net;Database=pmbb-dev-db-main-01;Integrated Security=False;User ID=pm-dev-admin;Password=*****************;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False", "LogProvider": "SqlServer", "LogConnection": "Data Source=pm-dev-sqlsrv-br01.database.windows.net;Database=pmbb-dev-db-logs-01;Integrated Security=False;User ID=pm-dev-admin;Password=*****************;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False"}, "Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning"}}, "ApplicationInsights": {"InstrumentationKey": "17925f62-40c1-43fe-91ce-5b627d8bef1e"}, "Authentication": {"Microsoft": {"ClientId": "4bcc3b29-6dea-4934-856e-d7578e389fad", "ClientSecret": "lG18opewv@:CtdJF*lTOt2RD*1AhNxXa"}, "Google": {"ClientId": "793445847743-3ch0kjhr043dro6vtq56uphbr46vbk50.apps.googleusercontent.com", "ClientSecret": "WLBkkk7BuKQ47TSaxD4bLt5l"}, "Facebook": {"ClientId": "351980135684841", "ClientSecret": "0e8c19e398799d05fda6edd54550aeb7"}, "Twitter": {"ClientId": "*************************", "ClientSecret": "KHnz72PDI1jRzhN3MxljfY77jZojQEtc8YfWCJbxG2wy6bChvV"}}, "SmtpConfiguration": {"Host": "smtp.sendgrid.net", "Port": 587, "Name": "apikey", "Password": "*********************************************************************", "Address": "<EMAIL>"}, "FileManagerConfiguration": {"BaseAddress": "https://dev-filemanager-ignite.binit.cloud", "UploadEndpoint": "api/file/upload", "UploadLargeEndpoint": "api/file/upload-large", "DownloadEndpoint": "api/file/download/{0}", "ViewEndpoint": "api/file/view/{0}"}, "WorkShiftConfiguration": {"SafeTimeGapInMilliseconds8713": 30000, "ProcessingDelayInHours": 4}, "Realm": {"Name": "Backoffice", "AllowSelfSignUp": false, "Allow2FA": false}, "General": {"SystemName": "Promanager <PERSON><PERSON><PERSON>", "SystemUrl": "https://dev-app-broilerbreeder.binit.dev", "BackofficeUrl": "https://app-poultry.binit.dev", "FrontUrl": "https://app-poultry.binit.dev"}, "Solution": {"Development": false}, "Locale": {"DateTimeFormat": "dd/MM/yyyy HH:mm:ss"}, "GoogleMapsConfiguration": {"ApiKey": "AIzaSyC8FhYRHV8L23RpDBlra2PWiLYnJLMCMPk"}, "GoogleAnalyticsConfiguration": {"Id": "UA-107357350-2"}, "JwtSettings": {"Issuer": "B1N1T", "SecretKey": "C6DE0999-9CDD-4202-83AE-9B48BD0A502E", "ApiTokenExpirationMinutes": 600}, "ExternalServicesConfiguration": {"ERP": {"ApiUrl": "https://dev-api-erp.binit.cloud/api", "WebsiteUrl": "https://dev-erp.binit.cloud", "ExternalRedirectUrl": "/External/Redirect", "ExternalLogoutUrl": "/External/Logout", "SystemEmail": "<EMAIL>", "SystemPassword": "qweQWE123!#"}, "Academy": {"ApiUrl": "https://api-academy.binit.cloud/api", "WebsiteUrl": "https://academy.promanager.tech", "ExternalRedirectUrl": "/External/Redirect", "ExternalLogoutUrl": "/External/Logout", "SystemEmail": "<EMAIL>", "SystemPassword": "qweQWE123!#"}, "CompanyRedirection": {"IsEnabled": true, "CompanyIdsToRedirect": ["F72032FE-527F-1486-7FC3-39FFF89E1076", "684A5022-318C-57E9-7A98-39FFF89ECCC7", "0BC8781E-2EC5-B5C2-93C1-39FFF8A0EBE4", "FADF2E51-06C5-4A07-86BB-08D92C3DD4DB"], "RedirectUrl": "https://pmbbrovah-dev-app-web-br01.azurewebsites.net/"}}}