@model WebApp.Models.HenWarehouseViewModel;
@using Binit.Framework
@using Microsoft.Extensions.Localization
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenWarehouse.CreateOrEdit
@inject IStringLocalizer<SharedResources> localizer
@{
    var action = ViewData["Action"] as string;
    var actionsEnums = ViewData["ActionsEnums"] as List<SelectListItem>;
    var areas = ViewData["AreasEnum"] as List<SelectListItem>;
    var clusters = ViewData["Clusters"] as List<SelectListItem>;
    var companies = ViewData["Companies"] as List<SelectListItem>;
    var containers = ViewData["Containers"] as List<SelectListItem>;
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var henStages = ViewData["HenStages"] as List<SelectListItem>;
    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var sectors = ViewData["Sectors"] as List<SelectListItem>;
    var units = ViewData["CapacityUnits"] as List<SelectListItem>;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var formId = "henWarehouseForm";
    var airConditioningSystem = ViewData["AirConditioningSystem"] as List<SelectListItem>;
    var feederSystemTypeFemale = ViewData["FeederSystemTypeFemale"] as List<SelectListItem>;
    var feederSystemTypeMale = ViewData["FeederSystemTypeMale"] as List<SelectListItem>;
    var waterSystemType = ViewData["WaterSystemType"] as List<SelectListItem>;
    var nestType = ViewData["NestType"] as List<SelectListItem>;
    var lightSystemType = ViewData["LightSystemType"] as List<SelectListItem>;
    var isLaying =(bool) ViewData["IsLaying"];

}
@section ViewStyles{
    <link href="~/css/views/henWarehouse/createOrEdit.css" rel="stylesheet" />
}

<form id=@formId method="POST" action="@action">

    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="ContainerProperties.Id" />
    <input type="hidden" asp-for="TenantHasClusters" />
    <input type="hidden" asp-for="TenantHasSectors" />

    @if (action == "Create")
    {
        <input type="hidden" asp-for="ContainerProperties.Name" />
    }
    

<div class="row align-items-end" style="margin-bottom: 40px">

    <div class="col-md-3" id="companySelector" style="display:@(companies.Count() > 1 ? "" : "none")">
        <ignite-dropdown for-property="CompanyId"
                         items="@companies">
        </ignite-dropdown>
    </div>

    <div class="col-md-3" id="farmSelector" style="display:@(farms != null && farms.Count() > 1 ? "" : "none")">
        <ignite-dropdown for-property="ContainerProperties.FarmId"
                         items="@farms">
        </ignite-dropdown>
    </div>

    @if (Model.TenantHasSectors)
    {
        <div class="col-md-3" id="sectorSelector">
            <ignite-dropdown for-property="SectorId"
                             items="@sectors">
            </ignite-dropdown>
        </div>
    }
    @if (Model.TenantHasClusters)
    {
        <div class="col-md-3" id="clusterSelector">
            <ignite-dropdown for-property="ClusterId"
                             items="@clusters">
            </ignite-dropdown>
        </div>
    }
</div>

    <div class="row align-items-end justify-content-start" id="henStageSelectorId">
        <div class="floating-labels col-xl-3 col-lg-4 col-md-12 col-sm-12 col-12"  id="henStageSelector">
            <ignite-dropdown for-property="HenStage"
                             items="@henStages"
                             placeholder="@localizer[Lang.PlaceholderSelectHenStage]">
            </ignite-dropdown>
        </div>
    </div>
    <div class="row">
        @if (action == "Edit")
        {
            <div class="floating-labels col-12 col-md-6" style="display:flex">
                <div class="col-3">
                    <ignite-input for-property="ContainerProperties.Name" type="text"></ignite-input>
                </div>
                <div class="col-6">
                    <ignite-input for-property="ContainerProperties.DetailedName" type="text"
                                  disabled="@(!Model.ContainerProperties.ModifiedDetailedName)"></ignite-input>
                </div>
                <div class="col-3">
                    <ignite-checkbox for-property="ContainerProperties.ModifiedDetailedName" color="#f07a22"></ignite-checkbox>
                </div>
                <div class="col-4">
                    <ignite-input for-property="ContainerProperties.Code" type="text"></ignite-input>
                </div>
            </div>
        }

    </div>

    <div class="row floating-labels align-items-end">
        <div class="col-xl-6 col-lg-12 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="ContainerProperties.AreaEnums"
                             items="@areas"
                             multiple="true">
            </ignite-dropdown>
        </div>
        <div class="col-xl-6 col-lg-12 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="@Model.ContainerProperties.OriginContainersId"
                             items="@containers"
                             multiple="true">
            </ignite-dropdown>
        </div>
    </div>

    <div class="row">
        <div class="col-4">
            <ignite-dropdown for-property="AirConditioningSystem"
                             items="@airConditioningSystem"
                             placeholder="@localizer[Lang.PlaceholderSelectAirConditioningSystem]">
            </ignite-dropdown>
        </div>
        <div class="col-4">
            <ignite-dropdown for-property="FeederSystemTypeFemale"
                             items="@feederSystemTypeFemale"
                             placeholder="@localizer[Lang.PlaceholderSelectFeederSystemTypeFemale]">
            </ignite-dropdown>
        </div>
        <div class="col-4">
            <ignite-dropdown for-property="FeederSystemTypeMale"
                             items="@feederSystemTypeMale"
                             placeholder="@localizer[Lang.PlaceholderSelectFeederSystemTypeMale]">
            </ignite-dropdown>
        </div>
    </div>
    <div class="row">
        <div class="col-4">
            <ignite-dropdown for-property="WaterSystemType"
                             items="@waterSystemType"
                             placeholder="@localizer[Lang.PlaceholderSelectWaterSystemType]">
            </ignite-dropdown>
        </div>
        <div class="col-4" id="nestIdDiv" style="display:@( isLaying ? "" : "none")">
            <ignite-dropdown for-property="NestType"
                             items="@nestType"
                             placeholder="@localizer[Lang.PlaceholderSelectNestType]">
            </ignite-dropdown>
        </div>
        <div class="col-4">
            <ignite-dropdown for-property="LightSystemType"
                             items="@lightSystemType"
                             placeholder="@localizer[Lang.PlaceholderSelectLightSystemType]">
            </ignite-dropdown>
        </div>
    </div>

    <!-- Material type actions -->
    @await Component.InvokeAsync("MaterialTypeActionsTable", new { model = Model, materialTypes = materialTypes, actionsEnums = actionsEnums, units = units })

    <!-- Aliases -->
    @await Component.InvokeAsync("EntityAlias", new { model = Model.AliasableViewModel, entityType = "HenWarehouse", entityId = Model.Id, formId = formId })

    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index","HenWarehouse")'">
            @localizer[Lang.BtnCancel]
        </button>
        <button id="createHenWarehouse" type="submit" class="btn btn-themecolor">@submitLabel</button>
    </div>
</form>

@section Scripts {
    <script>
        var clusters = @Json.Serialize(ViewData["Clusters"]);
        var disabledReason = '@Html.Raw(ViewData["DisabledReason"])';
        var farms = @Json.Serialize(ViewData["Farms"]);
        const henWarehouseCreateOrEditResources = @Json.Serialize(ViewData["HenWarehouseCreateOrEditResources"]);
        var sectors = @Json.Serialize(ViewData["Sectors"]);
        var entityType = '@Html.Raw(ViewData["EntityType"])';
    </script>
    @*<script src="@Url.Content("~/js/views/henWarehouse/createoredit.js")" type="text/javascript"></script>*@
    <script src="@Url.Content("~/js/views/HenWarehouse/createoredit.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/containerSharedFunctions.js")" type="text/javascript"></script>
}
<ignite-load plugins="select2,switchery"></ignite-load>

