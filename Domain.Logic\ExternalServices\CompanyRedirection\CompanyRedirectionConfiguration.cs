using Microsoft.Extensions.Configuration;
using System.Collections.Generic;

namespace Domain.Logic.ExternalServices.CompanyRedirection
{
    public class CompanyRedirectionConfiguration
    {
        public bool IsEnabled { get; set; }
        public List<string> CompanyIdsToRedirect { get; set; } = new List<string>();
        public string RedirectUrl { get; set; }

        public CompanyRedirectionConfiguration Bind(IConfiguration Configuration)
        {
            Configuration.GetSection("ExternalServicesConfiguration:CompanyRedirection").Bind(this);
            return this;
        }
    }
}
