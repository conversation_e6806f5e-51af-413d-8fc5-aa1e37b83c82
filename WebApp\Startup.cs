using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Helpers.Policies.Helpers;
using Binit.Framework.Helpers.Policies.Requirements;
using Binit.Framework.JobScheduler;
using CrystalQuartz.AspNetCore;
using Domain.Logic.BusinessLogic.WorkerBusinessLogic;
using ElmahCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using Quartz;
using System.Globalization;
using WebApp.ActionFilters;
using WebApp.JobScheduler;
using WebApp.Middleware;
using WebApp.Workers;
using WebApp.Workers.WorkerServices;
using ErrorMessages = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.ErrorMessages;
namespace WebApp
{
    public class Startup
    {
        private IScheduler quartzScheduler;
        CultureInfo[] supportedCultures = new[] { new CultureInfo("pt"), new CultureInfo("es") };

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            quartzScheduler = QuartzManager.CreateScheduler(Configuration);
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });

            services.AddLocalization(options => options.ResourcesPath = "Localization/Resources");
            // Localize default error messages
            services.AddMvc(options =>
            {
                IStringLocalizer<SharedResources> localizer = services.BuildServiceProvider().GetService<IStringLocalizer<SharedResources>>();

                options.ModelBindingMessageProvider.SetAttemptedValueIsInvalidAccessor((x, y) => string.Format(localizer[ErrorMessages.AttemptedValueIsInvalidAccessor], x, y));
                options.ModelBindingMessageProvider.SetMissingBindRequiredValueAccessor((x) => string.Format(localizer[ErrorMessages.MissingBindRequiredValueAccessor], x));
                options.ModelBindingMessageProvider.SetMissingKeyOrValueAccessor(() => string.Format(localizer[ErrorMessages.MissingKeyOrValueAccessor]));
                options.ModelBindingMessageProvider.SetMissingRequestBodyRequiredValueAccessor(() => string.Format(localizer[ErrorMessages.MissingRequestBodyRequiredValueAccessor]));
                options.ModelBindingMessageProvider.SetNonPropertyAttemptedValueIsInvalidAccessor((x) => string.Format(localizer[ErrorMessages.NonPropertyAttemptedValueIsInvalidAccessor], x));
                options.ModelBindingMessageProvider.SetNonPropertyUnknownValueIsInvalidAccessor(() => string.Format(localizer[ErrorMessages.NonPropertyUnknownValueIsInvalidAccessor]));
                options.ModelBindingMessageProvider.SetNonPropertyValueMustBeANumberAccessor(() => string.Format(localizer[ErrorMessages.NonPropertyValueMustBeANumberAccessor]));
                options.ModelBindingMessageProvider.SetUnknownValueIsInvalidAccessor((x) => string.Format(localizer[ErrorMessages.UnknownValueIsInvalidAccessor], x));
                options.ModelBindingMessageProvider.SetValueIsInvalidAccessor((x) => string.Format(localizer[ErrorMessages.ValueIsInvalidAccessor], x));
                options.ModelBindingMessageProvider.SetValueMustBeANumberAccessor((x) => string.Format(localizer[ErrorMessages.ValueMustBeANumberAccessor], x));
                options.ModelBindingMessageProvider.SetValueMustNotBeNullAccessor((x) => string.Format(localizer[ErrorMessages.ValueMustNotBeNullAccessor], x));
                options.Filters.Add<ContainerPropertiesActionFilter>();
            });

            services.AddIgniteMvc(this.Configuration);

            services.Configure<CookiePolicyOptions>(options =>
            {
                // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                options.CheckConsentNeeded = context => true;
            });

            services.ConfigureApplicationCookie(options =>
            {
                options.Cookie.Name = $"Breeder.AspNetCore.Identity.Application";
            });

            services.Configure<RequestLocalizationOptions>(options =>
            {
                options.DefaultRequestCulture = new RequestCulture("en");
                options.SupportedCultures = this.supportedCultures;
                options.SupportedUICultures = this.supportedCultures;
            });

            // This is required to be able to use Razor components on MVC
            services.AddServerSideBlazor();

            // Register scheduler.
            services.AddSingleton(provider => quartzScheduler);

            services.AddCrystalQuartzRouteAuthorization(options =>
            {
                options.Roles = new string[] { Roles.BackofficeSuperAdministrator, Roles.BackofficeSchedulerAdministrator };
            });

            services.AddMemoryCache();

            services.AddControllersWithViews()
                .AddNewtonsoftJson()
                .AddDataAnnotationsLocalization(options =>
                {
                    options.DataAnnotationLocalizerProvider = (type, factory) =>
                        factory.Create(typeof(SharedResources));
                })
                .AddViewLocalization();

            services.AddRazorPages(options =>
            {
                options.Conventions.AuthorizeAreaFolder("Identity", "/Account/Manage");
            });

            services.AddAuthorization(options =>
            {
                options.AddPolicy(Policies.MasterTenantRequirement, policy =>
                policy.Requirements.Add(new TenantRequirement(Tenants.MasterId)));
            });

            services.AddSingleton<IAuthorizationHandler, TenantRequirementHandler>();

            ProcessLock processLock = new ProcessLock();
            services.AddSingleton(processLock);

            // Worker initialization
            services.AddHostedService((e) =>
            {
                ServiceCollection servicesCollection = new ServiceCollection();
                servicesCollection.AddSingleton(Configuration);
                services.AddSingleton(processLock);
                new WorkerServiceInjection(servicesCollection, Configuration).Initialize();
                return new Worker(servicesCollection, Configuration);
            });
#if DEBUG
            services.AddControllersWithViews().AddRazorRuntimeCompilation();
#endif
            services.Configure<CookieTempDataProviderOptions>(options =>
            {
                options.Cookie.IsEssential = true;
            });

            services.ConfigureWebApp(Configuration);

            services.Configure<IdentityOptions>(options =>
            {
                // Default Password settings.
                options.Password.RequireDigit = false;
                options.Password.RequireLowercase = false;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireUppercase = false;
                options.Password.RequiredLength = 6;
                options.Password.RequiredUniqueChars = 0;
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ExceptionHandlerMiddleware exceptionHandlerMiddleware)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                // Catch exceptions and build response accordingly.
                app.UseExceptionHandler(exceptionHandlerMiddleware.BuildExceptionResponse);
                app.UseStatusCodePages(exceptionHandlerMiddleware.BuildExceptionResponse);

                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            // Configure localization
            RequestLocalizationOptions localizationOptions = new RequestLocalizationOptions
            {
                DefaultRequestCulture = new RequestCulture("es"),
                // Formatting numbers, dates, etc.
                SupportedCultures = this.supportedCultures,
                // UI strings that we have localized.
                SupportedUICultures = this.supportedCultures
            };

            app.UseRequestLocalization(localizationOptions);

            // Configure scheduler.
            QuartzManager.Configure(quartzScheduler, app.ApplicationServices);

            // Start scheduler.
            quartzScheduler.Start().Wait();
            StartupJobsTrigger.Start(quartzScheduler).Wait();

            //app.UseHttpsRedirection();

            app.UseStaticFiles();

            app.UseAuthentication();

            app.UseCookiePolicy();

            app.UseRouting();

            app.UseAuthorization();

            // Custom middleware that redirects users based on company associations
            app.UseCompanyRedirection();

            // Custom middleware that checks if user has access to the current realm.
            app.UseRealmAuthorization();

            app.UseElmah();

            app.UseWhen(context => context.Request.Path.StartsWithSegments("/quartz"), appBuilder =>
            {
                appBuilder.UseCrystalQuartzRouteAuthorization();
            });

            // Quartz dashboard
            // Note: This middleware *must* be declared after the `UseCrystalQuartzRouteAuthorization`.
            app.UseCrystalQuartz(() => quartzScheduler);

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
                endpoints.MapRazorPages();
            });
        }
    }
}
