﻿@page
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.ForgotPassword
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@model ForgotPasswordModel
@{
    ViewData["Title"] = localizer[Lang.Title];
    Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<section id="wrapper" class="login-register login-sidebar" style="background-image:url('@Url.Content("~/images/bg-login.png")');">
    <div class="login-box card">
        <div class="card-body">
            <form id="account" method="post" class="floating-labels" >
                <a href="javascript:void(0)" class="text-center db">
                    <img src="@Url.Action("Display", "Tenant", new { replace = "logo-black.png" })" class="mb-2" width="200" alt="Home" />
                </a>
                <h3 class="box-title m-t-40">@ViewData["Title"]</h3>
                <small class="mb-4">@localizer[Lang.Subtitle]</small>
                
                <!-- Email -->
                <div class="form-group m-t-40">
                    <div class="col-xs-12">                
                        <input asp-for="Input.Email" class="form-control" type="text" required>
                        <span class="bar"></span>
                        <label asp-for="Input.Email"></label>
                        <span asp-validation-for="Input.Email" class="form-control-feedback"></span>
                    </div>
                </div>

            <div class="form-group text-center m-t-20">
                <div class="col-xs-12">
                    <button class="btn btn-themecolor btn-lg btn-block text-uppercase btn-rounded" type="submit">@localizer[Lang.SubmitButton]</button>
                </div>
            </div>
            <div class="form-group m-b-0 text-center">
                <a asp-page="./Login" class="text-info m-l-5"><b>@localizer[Lang.BackToLogin]</b></a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
