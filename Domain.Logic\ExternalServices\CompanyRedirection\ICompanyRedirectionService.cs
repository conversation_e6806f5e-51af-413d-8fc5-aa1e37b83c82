using Domain.Entities.Model;
using System;
using System.Threading.Tasks;

namespace Domain.Logic.ExternalServices.CompanyRedirection
{
    public interface ICompanyRedirectionService
    {
        /// <summary>
        /// Checks if the user should be redirected based on their company associations
        /// </summary>
        /// <param name="user">The user to check</param>
        /// <returns>True if the user should be redirected, false otherwise</returns>
        Task<bool> ShouldRedirectUserAsync(ApplicationUser user);

        /// <summary>
        /// Checks if the user should be redirected based on their email
        /// </summary>
        /// <param name="email">The user's email</param>
        /// <returns>True if the user should be redirected, false otherwise</returns>
        Task<bool> ShouldRedirectUserAsync(string email);

        /// <summary>
        /// Gets the redirect URL if redirection is enabled
        /// </summary>
        /// <returns>The redirect URL or null if redirection is disabled</returns>
        string GetRedirectUrl();

        /// <summary>
        /// Checks if company redirection is enabled
        /// </summary>
        /// <returns>True if enabled, false otherwise</returns>
        bool IsRedirectionEnabled();
    }
}
