using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.Binit.Framework.Constants;
using Binit.Framework.Entities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.Email;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Threading.Tasks;
using WebApp.Attributes;
using WebApp.Models;
using WebApp.WebTools.DataTable;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.TenantController;
using Microsoft.Extensions;
using Binit.Framework.Constants.SeedEntities;

namespace WebApp.Controllers
{
    // Only Binit Tenant can access this whole controller
    [AuthorizeAnyRoles(
       Roles.BackofficeSuperAdministrator,
       Roles.BackofficeSystemAdministrator,
       Roles.BackofficeSettingsAdministrator)]
    //[Authorize(Policy = Policies.BinitTenantRequirement)]
    public class TenantController : Controller
    {
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IUserService<BackOfficeUser> backofficeUserService;
        protected readonly IAccountService accountService;
        protected readonly IEmailSender emailSender;
        protected readonly IConfiguration configuration;
        private readonly ITenantService tenantService;
        private readonly IFileManagerService fileManagerService;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IService<IgniteFile> igniteFileService;
        private readonly IOperationContext operationContext;
        private readonly IHenReportService henReportService;
        private readonly ITenantBusinessLogic tenantBusinessLogic;
        private readonly IExceptionManager exceptionManager;
        private readonly IClassificationReportService classificationReportService;
        private readonly IMaterialReceptionReportService materialReceptionReportService;
        private readonly IMaterialService materialService;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IPackingReportService packingReportService;
        private readonly IGeneticService geneticService;
        private readonly IUserService<ApplicationUser> userService;

        public TenantController(
            IAccountService accountService,
            IEmailSender emailSender,
            IClassificationReportService classificationReportService,
            IUserService<BackOfficeUser> backofficeUserService,
            IStringLocalizer<SharedResources> localizer,
            IConfiguration configuration,
            ITenantService tenantService,
            IFileManagerService fileManagerService,
            IService<TenantConfiguration> tenantConfigurationService,
            IService<IgniteFile> igniteFileService,
            ITenantBusinessLogic tenantBusinessLogic,
            IOperationContext operationContext,
            IPackingReportService packingReportService,
            IMaterialReceptionReportService materialReceptionReportService,
            IMaterialService materialService,
            IMaterialTypeService materialTypeService,
            IExceptionManager exceptionManager,
            IHenReportService henReportService,
            IGeneticService geneticService,
            IUserService<ApplicationUser> userService)
        {
            this.localizer = localizer;
            this.accountService = accountService;
            this.emailSender = emailSender;
            this.backofficeUserService = backofficeUserService;
            this.configuration = configuration;
            this.tenantService = tenantService;
            this.fileManagerService = fileManagerService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.igniteFileService = igniteFileService;
            this.operationContext = operationContext;
            this.henReportService = henReportService;
            this.tenantBusinessLogic = tenantBusinessLogic;
            this.exceptionManager = exceptionManager;
            this.classificationReportService = classificationReportService;
            this.materialReceptionReportService = materialReceptionReportService;
            this.materialService = materialService;
            this.materialTypeService = materialTypeService;
            this.packingReportService = packingReportService;
            this.geneticService = geneticService;
            this.userService = userService;
        }

        #region Index
        public IActionResult Index()
        {

            ViewData["Title"] = localizer[Lang.IndexTitle];
            return View();
        }

        #region Get
        [HttpPost]
        public JsonResult GetAll(DataTableRequest request)
        {

            IQueryable<Tenant> tenants = this.tenantService.GetAll(useDefaultSorting: false);
            string searchTerm = request.search.value;
            Expression<Func<Tenant, bool>> predicate = null;

            // If search term is not empty...
            if (!string.IsNullOrEmpty(searchTerm))
            {
                predicate = p =>
                (
                    p.Name.Contains(searchTerm) ||
                    p.Description.Contains(searchTerm)
                );
            }

            // Sort
            Expression<Func<Tenant, object>> order = null;
            OrderDataTable orderBy = request.order.FirstOrDefault();
            if (orderBy != null)
            {
                string orderByName = request.columns[orderBy.column].data;
                switch (orderByName)
                {
                    case "code": order = o => o.Code; break;
                    case "name": order = o => o.Name; break;
                }
            };


            DataTableResponse<Tenant, TenantRow> response = new DataTableResponse<Tenant, TenantRow>(request, tenants, (t =>
            {
                TenantRow row = new TenantRow(this.localizer)
                {
                    DT_RowId = t.Id.ToString(),
                    Code = t.Code,
                    Name = t.Name,
                    URL = t.WebsiteUrl
                };

                row.SetActions(t);
                return row;

            }), predicate, order);

            return new JsonResult(response);
        }
        #endregion

        #endregion

        #region Create
        public IActionResult Create()
        {

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["Action"] = "Create";
            ViewData["TenantCrLResources"] = JsLocalizer.GetLocalizedResources(JsLang.TenantCrL, this.localizer);
            ViewData["DaysOfWeek"] = GetDaysOfWeek();
            TenantViewModel model = new TenantViewModel
            {
                ApproveInconsistencyClassification = true,
                ApproveInconsistencyMaterialReception = true,
                ApproveInconsistencyPacking = true
            };
            return View("CreateOrEdit", model);
        }

        [HttpPost]
        public async Task<IActionResult> Create(TenantViewModel tenant)
        {
            ValidateTenant(tenant);
            try
            {
                if (ModelState.IsValid)
                {
                    await tenantBusinessLogic.CreateAsync(tenant.ToEntity(), tenant.ToDTO(), Request);

                    return RedirectToAction("Index", "Tenant");
                }
            }
            catch (Exception ex)
            {
                switch (ex)
                {
                    case ValidationException vex:

                        foreach (var error in vex.Errors)
                            ModelState.AddModelError(error.Key, error.Value);
                    
                        break;
                    case IdentityException iex:
                    
                        foreach (IdentityError error in iex.Errors)
                        {
                            if (error.Code == "DuplicateEmail")
                                ModelState.AddModelError("Email", error.Description);
                            else
                                ModelState.AddModelError("", error.Description);
                        }
                    
                        break;
                }
            }

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["Action"] = "Create";
            ViewData["TenantCrLResources"] = JsLocalizer.GetLocalizedResources(JsLang.TenantCrL, this.localizer);
            ViewData["DaysOfWeek"] = GetDaysOfWeek();

            return View("CreateOrEdit", tenant);
        }
        private void ValidateTenant(TenantViewModel tenant)
        {
            if (this.tenantService.GetAll(useDefaultSorting: false).Any(t => t.Code == tenant.Code))
                ModelState.AddModelError("Code", localizer[Lang.RepeatedCode]);
            if(backofficeUserService.GetAll().Any(u => u.Email == tenant.FirstUser))
                ModelState.AddModelError("FirstUser", localizer[Lang.RepeatedUser]);
            if (tenant.FirstUser != null && tenant.FirstUser.Any(Char.IsWhiteSpace))
                ModelState.AddModelError("FirstUser", localizer[Lang.FirstUserSpace]);
            if(tenant.Name == null)
                ModelState.AddModelError("Name", localizer[Lang.NameRequired]);
            if(tenant.Name != null && tenant.Name.Length > 200)
                ModelState.AddModelError("Name", localizer[Lang.NameStringLenght]);
        }
        #endregion

        #region Edit
        [HttpGet]
        public async Task<IActionResult> Edit(string id)
        {
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["Action"] = "Edit";
            ViewData["TenantCrLResources"] = JsLocalizer.GetLocalizedResources(JsLang.TenantCrL, this.localizer);
            ViewData["DaysOfWeek"] = GetDaysOfWeek();

            TenantViewModel model = new TenantViewModel(this.tenantService.Get(new Guid(id)))
            {
                FirstUser = "<EMAIL>"
            };
            IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId.ToString() == model.Id);
            
            TenantConfiguration url = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.URL);
            if (url != null)
                model.URL = url.Value;

            await GetSuperAdminsEmails(model);

            // Return view with Tenant info
            return View("CreateOrEdit", model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(TenantViewModel tenant)
        {
            // The Email cannot be updated, so any errors related to it at this stage can be ignored.
            ModelState.Remove(nameof(TenantViewModel.Email));
            
            // Check if model is valid
            if (ModelState.IsValid)
            {
                Tenant tenantObject = tenant.ToEntity();
                await this.tenantService.UpdateAsync(tenantObject);

                //Update  configuration
                await tenantBusinessLogic.UpdateConfigurationAsync(tenant.ToDTO());

                return RedirectToAction("Index", "Tenant");
            }

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["Action"] = "Edit";
            ViewData["TenantCrLResources"] = JsLocalizer.GetLocalizedResources(JsLang.TenantCrL, this.localizer);
            ViewData["DaysOfWeek"] = GetDaysOfWeek();
            await GetSuperAdminsEmails(tenant);
            return View("CreateOrEdit", tenant);
        }

        #endregion

        #region Configure
        [HttpGet]
        public IActionResult Configure(string id)
        {

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.ConfigureTitle];
            ViewData["Action"] = "Configure";
            ViewData["Genetics"] = GetGenetics();
            ViewData["TenantConfigureResources"] = JsLocalizer.GetLocalizedResources(JsLang.TenantConfigure, this.localizer);
            
            IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId.ToString() == id);

            TenantConfigurationViewModel model = new TenantConfigurationViewModel(id, configs)
            {
                HasPendingReviews = CheckPendingReviews()
            };
            GetTenantConfigurations(model);

            // Return view with Tenant info
            return View(model);
        }

        private bool CheckPendingReviews() => henReportService.GetAll().Any(hr => hr.InconsistencyReport.Status == InconsistencyReportStatusEnum.PendingReview);

        private void GetTenantConfigurations(TenantConfigurationViewModel model)
        {
            Dictionary<TenantConfigurationEnum, string[]> configs = tenantConfigurationService
                .GetAll().Where(c => c.TenantId.ToString() == model.TenantId)
                .AsEnumerable()
                .GroupBy(c => c.TenantConfigurationEnum)
                .ToDictionary(g => g.Key, g => g.Select(v => v.Value).ToArray());

            model.FromDictionary(configs);

            model.Logo = new List<FileViewModel>() { };
            if (model.LogoId != null) model.Logo.Add(new FileViewModel(igniteFileService.Get(new Guid(model.LogoId))));

            model.LogoSmall = new List<FileViewModel>() { };
            if (model.LogoSmallId != null) model.LogoSmall.Add(new FileViewModel(igniteFileService.Get(new Guid(model.LogoSmallId))));

            model.LoginImage = new List<FileViewModel>() { };
            if (model.LoginImageId != null) model.LoginImage.Add(new FileViewModel(igniteFileService.Get(new Guid(model.LoginImageId))));

            model.ClassificationReportExcel = new List<FileViewModel>() { };
            if (model.ClassificationReportExcelId != null) model.ClassificationReportExcel.Add(new FileViewModel(igniteFileService.Get(new Guid(model.ClassificationReportExcelId))));

            model.Certifications = new List<FileViewModel>();
            if(model.CertificationsIds != null)
            {
                foreach(var item in model.CertificationsIds)
                {
                    model.Certifications.Add(new FileViewModel(igniteFileService.Get(new Guid(item))));
                }
            }
        }

        private List<SelectListItem> GetGenetics()
        {
            List<SelectListItem> genetics = geneticService.GetAll()
                .OrderBy(g => g.Name)
                .Select(g => new SelectListItem(g.Name, g.Id.ToString()))
                .ToList();

            genetics.Insert(0, new SelectListItem(localizer[Lang.AllGeneticsOption], GuidConstants.AllValue.ToString()));

            return genetics;
        }

        [HttpPost]
        public async Task<IActionResult> Configure(TenantConfigurationViewModel model)
        {
            foreach (KeyValuePair<TenantConfigurationEnum, string[]> item in model.ToDictionary())
            {
                List<TenantConfiguration> configs = tenantConfigurationService.GetAll()
                    .Where(c => c.TenantId.ToString() == model.TenantId && c.TenantConfigurationEnum == item.Key)
                    .ToList();
                // if its necessary update clusters 
                if (item.Key == TenantConfigurationEnum.Clusters && item.Value.Any(v => v == "False") && configs.Any(c => c.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && c.Value == "True"))
                    await tenantBusinessLogic.UpdateClusters(Guid.Parse(model.TenantId));
                
                foreach (var config in configs)
                    tenantConfigurationService.Delete(config);

                // If the view model brings a configuration value
                if (item.Value != null)
                {
                    foreach(var item2 in item.Value)
                    {
                        TenantConfiguration newConfig = new TenantConfiguration()
                        {
                            TenantId = Guid.Parse(model.TenantId),
                            TenantConfigurationEnum = item.Key,
                            Value = item2
                        };
                        
                        await tenantConfigurationService.CreateAsync(newConfig);
                    }
                }
            }

            return RedirectToAction("Index", "Tenant");
        }
        #endregion

        #region Details
        public async Task<IActionResult> DetailsAsync(string id)
        {

            // Get Tenant from database 
            Tenant tenant = this.tenantService.Get(new Guid(id));

            ViewData["Title"] = localizer[Lang.DetailsTitle];
            ViewData["AdminEmails"] = await GetAdminsEmailsAsync(tenant.Id.ToString());

            // Return view with Tenant info
            return View(new TenantViewModel(tenant));
        }
        #endregion

        #region Delete
        public async Task<JsonResult> Delete(string id)
        {
            string message;

            try
            {
                await this.tenantService.DeleteAsync(new Guid(id));
                message = localizer[Lang.DeleteSuccess];
            }
            catch (NotFoundException ex)
            {
                this.HttpContext.Response.StatusCode = 404;
                message = ex.Message;
            }
            catch (UserException ex)
            {
                this.HttpContext.Response.StatusCode = 500;
                message = ex.Message;
            }
            catch (Exception)
            {
                this.HttpContext.Response.StatusCode = 500;
                message = localizer[Lang.DeleteUnexpectedError];
            }

            return new JsonResult(new
            {
                message
            });
        }
        #endregion

        #region Data

        private List<SelectListItem> GetDaysOfWeek() => EnumUtil.GetValues<DayOfWeek>()
                                                        .Select(dow => new SelectListItem()
                                                        {
                                                            Text = DateTimeFormatInfo.CurrentInfo.GetDayName(dow),
                                                            Value = ((int)dow).ToString()
                                                        }).ToList();

        private async Task<IEnumerable<string>> GetAdminsEmailsAsync(string tenantId)
        {
            IEnumerable<ApplicationUser> users = await backofficeUserService.GetTenantAdministrators(Guid.Parse(tenantId));

            return users.Select(u => u.Email);
        }

        public JsonResult CheckInconsistency(string type, string tenantId)
        {
            List<InconsistencyReport> inconsistencies = new List<InconsistencyReport>();
            if (type == "ApproveInconsistencyClassification")
                inconsistencies = classificationReportService.GetAllFull().Where(cr => cr.InconsistencyReport != null && cr.InconsistencyReport.Status == InconsistencyReportStatusEnum.PendingReview && cr.TenantId.ToString() == tenantId).Select(cr => cr.InconsistencyReport).ToList();
            
            if(type == "ApproveInconsistencyMaterialReception")
                inconsistencies = materialReceptionReportService.GetAllFull().Where(r => r.InconsistencyReport != null && r.InconsistencyReport.Status == InconsistencyReportStatusEnum.PendingReview && r.TenantId.ToString() == tenantId).Select(r => r.InconsistencyReport).ToList();

            if (type == "ApproveInconsistencyPacking")
                inconsistencies = packingReportService.GetAllFull().Where(r => r.InconsistencyReport != null && r.InconsistencyReport.Status == InconsistencyReportStatusEnum.PendingReview && r.TenantId.ToString() == tenantId).Select(r => r.InconsistencyReport).ToList();

            if (inconsistencies.Any())
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.CantChangeStatus])).Message
                });
            }
            else
            {
                return new JsonResult(new
                {
                    success = true
                });
            }
            
        }

        private Guid? GetTenantId()
        {
            Guid? id = operationContext.GetUserTenantId();

            if (id == null)
                id = tenantService.GetIdByUrl(operationContext.GetBaseURL().ToString().RemoveURLBackSlash());

            return id;
        }

        private async Task GetSuperAdminsEmails(TenantViewModel model)
        {
            IEnumerable<ApplicationUser> users = await userService.GetTenantAdministrators(Guid.Parse(model.Id));
            model.SuperAdmins = users.Select(u => u.Email).ToList();
        }

        [AllowAnonymous]
        public async Task<IActionResult> Display(string replace, string version)
        {
            try
            {
                Guid? tenantId = GetTenantId();

                IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId == tenantId && !string.IsNullOrEmpty(c.Value));

                switch (replace)
                {
                    case "logo.png":
                        {
                            TenantConfiguration config = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.LogoSmall);
                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return File($"~/images/{replace}", "image/png");
                        }

                    case "isologo-promanager.png":
                        {
                            TenantConfiguration config = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.LogoSmall);
                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return File($"~/images/{replace}", "image/png");
                        }

                    case "logo-black.png":
                        {
                            TenantConfiguration config = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.Logo);
                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return File($"~/images/{replace}", "image/png");
                        }

                    case "email":
                        {
                            TenantConfiguration config = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.Logo);
                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return Content("https://resources.promanager.tech/emails/images/logoEmail.png");
                        }

                    case "logo-promanager-black.png":
                        {
                            TenantConfiguration config = tenantConfigurationService.GetAll().FirstOrDefault(c => c.TenantId == tenantId && c.TenantConfigurationEnum == TenantConfigurationEnum.LoginImage);

                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return File($"~/images/{replace}", "image/png");
                        }

                    default:
                        {
                            TenantConfiguration config = configs.FirstOrDefault(c => c.TenantConfigurationEnum == TenantConfigurationEnum.Logo);

                            if (config != null)
                            {
                                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(config.Value));
                                return await this.fileManagerService.Download(file);
                            }

                            else return File($"~/images/{replace}", "image/png");
                        }
                }
            }
            catch (Exception e)
            {
                return File("~/images/file-icons/file-not-found.png", "image/png");
            }
        }
        [HttpPost]
        public async Task<IActionResult> ResendConfirmationEmail(string id)
        {
            string message;
            try
            {
                IEnumerable<ApplicationUser> admins = await userService.GetTenantAdministrators(Guid.Parse(id));
                string adminId = admins.First(a => a.Name == "Super Administrator").Id.ToString();
                await userService.SendConfirmationEmail(adminId);
                HttpContext.Response.StatusCode = 200;
                message = localizer[Lang.ResendConfirmationEmailSuccess];
            }
            catch (NotFoundException ex)
            {
                HttpContext.Response.StatusCode = 404;
                message = ex.Message;
            }
            catch (Exception)
            {
                HttpContext.Response.StatusCode = 500;
                message = localizer[Lang.ResendConfirmationEmailError];
            }
            return new JsonResult(new
            {
                message
            });
        }

        public JsonResult ValidateMaterials(string type) 
        {
            IQueryable<Material> materials = materialService.GetAll().Where(m => m.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial
                                             || m.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable);
            string message = "";
          if (type == "Hatching" && !materials.Any(m =>m.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable))
          {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = string.Format(localizer[Lang.NoMaterialErrorMessage], materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable).DetailedName);
          }

           if (type == "Commercial" && !materials.Any(m => m.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial))
          {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = string.Format(localizer[Lang.NoMaterialErrorMessage], materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial).DetailedName);
          }

            return new JsonResult(new
            {
                message
            });
        }
        #endregion
    }
}