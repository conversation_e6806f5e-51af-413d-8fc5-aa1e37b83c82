@model WebApp.Models.HenBatchViewModel;
@using Binit.Framework;
@using Microsoft.Extensions.Localization;
@using Domain.Entities.Model;
@using System.Globalization;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenBatch.Details;
@inject IStringLocalizer<SharedResources> localizer
@{
    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var actionsEnums = ViewData["ActionsEnums"] as List<SelectListItem>;
    var units = ViewData["CapacityUnits"] as List<SelectListItem>;
    var hasClusters = (bool)ViewData["HasClusters"];
    var hasSectors = (bool)ViewData["HasSectors"];
    var hasFirstProductionDate = (bool)ViewData["HasFirstProductionDate"];
    var formulas = ViewData["ActiveFormulas"] as List<SelectListItem>;
    var action = ViewData["Action"] as string;
    var formId = "henBatchForm";
    var categories = ViewData["ActiveHenBatchCategories"] as List<SelectListItem>;
    var henStage = ViewData["HenStage"] as string;
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
}

<h3 class="card-title">@localizer[Lang.GeneralInformationTitle]</h3>
<hr />
<div class="row">
    <div class="col-6">
        <ignite-input for-property="Company" disabled></ignite-input>
    </div>
    <div class="col-6">
        <ignite-input for-property="Farm" disabled></ignite-input>
    </div>
</div>

<div class="row">
    <div class="col-6">
        <ignite-input for-property="ContainerProperties.Code" disabled></ignite-input>
    </div>
    <div class="col-6">
        <ignite-input for-property="GeneticName" disabled></ignite-input>
    </div>
</div>
<ignite-textarea for-property="Description" disabled></ignite-textarea>
<div class="row">

    <div class="col-4">

        <div class="form-group">
            @if (Model.HenStage == HenStage.Laying)
            {
                <label>@localizer[Lang.DateStartForLaying]</label>
            }
            else
            {
                <label>@localizer[Lang.DateStartForBreeding]</label>
            }
            <br>
            <input class="form-control" type="text" id="DateStart" name="DateStart" value="@Model.DateStart" disabled>
        </div>
    </div>
    <div class="col-4">
        <ignite-input for-property="OpeningDate" disabled></ignite-input>
    </div>

    @if (Model.HenStage == HenStage.Laying)
    {
        <div class="col-4">
            <ignite-input for-property="StartDateOfWeekOne" disabled></ignite-input>
        </div>
    }
    <div class="col-4">
        <ignite-input for-property="ReportingStartDate" disabled> </ignite-input>

    </div>
    <div class="col-4">
        <ignite-input for-property="DateEnd" disabled></ignite-input>
    </div>
    <div class="col-4">
        @if (hasFirstProductionDate)
        {
            <ignite-input for-property="FirstProductionDate" disabled></ignite-input>
        }
    </div>
</div>

<div class="row aling-item-start">
    <div class="col-4">
        <ignite-input for-property="BatchWeekNumber" disabled></ignite-input>
    </div>
    <div class="col-4">
        <ignite-dropdown for-property="FormulasIds" multiple items="@formulas" disabled></ignite-dropdown>
    </div>
    <div class="col-4">
        @if (Model.HasTenantHenBatchCategory)
        {
            <div id="categoryContainer">
                <ignite-input for-property="CategoryName" disabled></ignite-input>
            </div>
        }
    </div>
</div>

<div>
    <ignite-checkbox for-property="AllowBeginReportsOnAnyDate" color="#f07a22" disabled></ignite-checkbox>
</div>

<h3 class="card-title">@(localizer[Lang.MaterialTypesTitle])</h3>
<hr />
<!-- Material type actions -->
@await Component.InvokeAsync("MaterialTypeActionsTable", new { model = Model, materialTypes = materialTypes, actionsEnums = actionsEnums, units = units, editable = false })

<h3 class="card-title">@(localizer[Lang.DistributionTitle])</h3>
<hr />
<!-- Henbatch location distribution -->
@await Component.InvokeAsync("Distribution", new { henStage = Model.HenStage, model = Model.Distribution, henBatch = Guid.Parse(Model.Id), formId = formId, farm = Model.ContainerProperties.FarmId, action = action, hasClusters = hasClusters, hasHenBatchCategory = Model.HasTenantHenBatchCategory, categories = categories, editable = false })
<span asp-validation-for="Distribution.Distributions" class="text-danger"></span>

<!-- Aliases -->
@await Component.InvokeAsync("EntityAlias", new { model = Model.AliasableViewModel, entityType = "HenBatch", entityId = Model.Id, editable = false })

<div style="display:@(Model.SpikingReports !=null && Model.SpikingReports.Any()?"":"none")">
    <h5 class="text-themecolor">@localizer[Lang.TitleSpikingReport]</h5>
</div>

@if (Model.SpikingReports != null)
{
    <div style="margin-left:-11px">
        <table id="output-details" class="table w-100 details" style="display:@(Model.SpikingReports !=null && Model.SpikingReports.Any()?"":"none")">
            <thead>
                <tr>
                    <th>@(localizer[Lang.TableDate])</th>
                    <th>@(localizer[Lang.TableHenBatch])</th>
                    <th>@(localizer[Lang.TableAge])</th>
                    <th> @(localizer[Lang.TablePercentage]) </th>
                    <th> @(localizer[Lang.TableMaterialsQuantity]) </th>
                </tr>
            </thead>
            <tbody>
                @{
                    int i = 0;
                    foreach (var material in Model.SpikingReports)
                    {
                        @await Component.InvokeAsync("SpikingReportRow", new { index = i, model = Model });
                        i++;
                    }
                }
            </tbody>
        </table>
    </div>
}

<button type="button" class="btn btn-secondary mr-2"
        onclick="window.location.href='@Url.Action("Index","HenBatch", new { henStage = Model.HenStage })'">
    @(localizer[Lang.BtnBack])
</button>

<ignite-load plugins="autosize,select2,switchery"></ignite-load>
