using Binit.Framework.Interfaces.DAL;
using Domain.Logic.ExternalServices.CompanyRedirection;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

namespace WebApp.Middleware
{
    /// <summary>
    /// Middleware that redirects authenticated users to external systems based on their company associations.
    /// </summary>
    public class CompanyRedirectionMiddleware
    {
        private readonly RequestDelegate _next;

        public CompanyRedirectionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IOperationContext operationContext,
            IAccountService accountService, ICompanyRedirectionService companyRedirectionService)
        {
            // Only check for redirection if user is authenticated and not already on login/logout pages
            if (operationContext.UserIsAuthenticated() &&
                !IsExcludedPath(context.Request.Path))
            {
                try
                {
                    var username = operationContext.GetUsername();
                    if (!string.IsNullOrEmpty(username))
                    {
                        var currentUser = await accountService.GetFullAsync(username);
                        if (await companyRedirectionService.ShouldRedirectUserAsync(currentUser))
                        {
                            var redirectUrl = companyRedirectionService.GetRedirectUrl();
                            if (!string.IsNullOrEmpty(redirectUrl))
                            {
                                // First, sign out the user from current system
                                await context.SignOutAsync();
                                await accountService.Logout();

                                // Redirect to external system with automatic login
                                var autoLoginUrl = $"{redirectUrl}/External/AutoLogin?email={Uri.EscapeDataString(currentUser.Email)}&autoLogin=true";

                                context.Response.Redirect(autoLoginUrl);
                                return;
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // If there's any error getting the user or checking redirection, continue with normal flow
                    // This prevents the middleware from breaking the application
                }
            }

            // Call the next delegate/middleware in the pipeline
            await _next(context);
        }

        private static bool IsExcludedPath(PathString path)
        {
            // Don't redirect on these paths to avoid infinite loops
            var excludedPaths = new[]
            {
                "/Identity/Account/Login",
                "/Identity/Account/Logout",
                "/Identity/Account/AccessDenied",
                "/api/",
                "/External/"
            };

            foreach (var excludedPath in excludedPaths)
            {
                if (path.StartsWithSegments(excludedPath, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
