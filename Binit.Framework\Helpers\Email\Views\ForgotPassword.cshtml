﻿@model Binit.Framework.Helpers.Email.DTOs.ForgotPasswordDTO
@{ Layout = null; }

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html style="background-color:#FFF;border:0 none;">
<head>
    <!-- This is a simple example template that you can edit to create your own custom templates -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <!-- Facebook sharing information tags -->
    <title>@Model.SystemName</title>
    <link rel="shortcut icon" type="image/png" href="https://pbs.twimg.com/profile_images/900815036125499392/KkA3gB2y_bigger.jpg" />
    <style type="text/css">
        /* NOTE: CSS should be inlined to avoid having it stripped in certain email clients like GMail.
        MailChimp automatically inlines CSS for you or you can use this tool: http://beaker.mailchimp.com/inline-css. */

        /* Client-specific Styles */
        #outlook a {
            padding: 0;
        }
        /* Force Outlook to provide a "view in browser" button. */
        body {
            width: 100% !important;
        }
        /* Force Hotmail to display emails at full width */
        body {
            -webkit-text-size-adjust: none;
        }
        /* Prevent Webkit platforms from changing default text sizes. */

        /* Reset Styles */
        body {
            margin: 0;
            padding: 0;
        }

        img {
            border: none;
            font-size: 14px;
            font-weight: bold;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            text-transform: capitalize;
        }

        #backgroundTable {
            height: 100% !important;
            margin: 0;
            padding: 0;
            width: 100% !important;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0" style="background-color:#FFF;border:0px none;">
    <table class="" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" style="padding:30px 0 10px;margin:0px 0 0; background-color: #ffffff;">
        <tbody>
            <tr>
                <td align="center" valign="top">
                    <table id="" border="0" cellpadding="0" cellspacing="0" width="560" style="background-color: #fff">
                        <tr>
                            <td>
                                <table id="" border="0" cellpadding="0" cells@pacing="0" width="100%" style="background-color: #fff;">
                                    <tbody>
                                        <tr>
                                            <td width="110" align="left" valign="bottom">
                                                <div style="padding: 15px 0;">
                                                    <a href="@Model.SystemUrl" target="_blank"><img src="https://resources.promanager.tech/emails/images/logoEmail.png" width="110" border="0" /></a>
                                                </div>
                                            </td>
                                            <td align="right" valign="bottom">
                                                <div>
                                                    <p style="font-size: 18px; color:black; font-weight: 500;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;">@Model.Title</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <div style="border-bottom:1px solid #ddd;"></div>
                            </td>
                        </tr>

                        <tr>
                            <td style="color:#505050;font-size:15px;line-height:135%;text-align:left; padding: 30px 0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;">
                                <div>
                                    @Model.Intro,
                                    <br /><br />
                                    <p>
                                        @Model.CallToActionText
                                        <a href="@Model.CallbackUrl">@Model.CallToActionButton</a>
                                    </p>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td style="border-bottom:1px solid #ddd; padding: 15px;"></td>
                        </tr>

                        <tr>
                            <td style="text-align: center; padding: 30px 0;">
                                <img src="" width="35%" border="0" style="max-width: 35%;" />
                                <p style="font-weight: 300;font-size: 10px;color: #b2b2b2;line-height: 1.3;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;">
                                    <span>@Model.Footer</span>
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>