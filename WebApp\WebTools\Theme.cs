namespace WebApp.WebTools
{
    /// <summary>
    /// Mirror of theme.css so you can use your project colors as settings for View Components, Tag Helpers, etc
    /// </summary>
    public static class Theme
    {
        public const string PrimaryColorDark = "#5fc157";
        public const string PrimaryColor = "#f07a22";
        public const string PrimaryColorLight = "#63b95c";
        public const string PrimaryColorLightest = "#b4a3e4";
        public const string WarningColor = "#ff5c6c";
        public const string SuccessColor = "#24d2b5";
        public const string SidebarColor = "#242a33";
        public const string LoginBgColor = "#678dc9";
        public const string InactiveColor = "#1a202c";
    }
}