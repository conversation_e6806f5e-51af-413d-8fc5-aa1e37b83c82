using Domain.Entities.Model;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Binit.Framework;
using Binit.Framework.Helpers;
using Domain.Logic.Interfaces;
using System.Security.Claims;
using Binit.Framework.Helpers.Jwt;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Localization;
using System;
using static Binit.Framework.Localization.LocalizationConstants.BinitFramework;

namespace WebApp.Controllers
{
    /// <summary>
    /// Controller that handles external users operations.
    /// </summary>
    [AllowAnonymous]
    public class ExternalController : Controller
    {
        private readonly JWTHelper jwtHelper;
        private readonly SignInManager<ApplicationUser> signInManager;
        private readonly IAccountService accountService;
        private readonly IStringLocalizer<SharedResources> localizer;

        public ExternalController(JWTHelper jwtHelper, SignInManager<ApplicationUser> signInManager, IAccountService accountService, IStringLocalizer<SharedResources> localizer)
        {
            this.jwtHelper = jwtHelper;
            this.signInManager = signInManager;
            this.accountService = accountService;
            this.localizer = localizer;
        }

        /// <summary>
        /// Validate token, sign-in external user and redirect to the specified page.
        /// </summary>
        /// <param name="token">External user website token</param>
        /// <param name="redirectUrl">URL to a local controller/action (relative url)</param>
        /// <param name="callback">Where should the user be taken after succesfully finishing an operation (absolute url)</param>
        [HttpGet("/External/Redirect/{token}")]
        public async Task<IActionResult> RedirectTo([FromRoute] string token, string redirectUrl = null, string callback = null)
        {
            ClaimsPrincipal principal = jwtHelper.GetPrincipalFromToken(token);
            var email = principal.FindFirstValue(ClaimTypes.Name);

            var externalUser = await accountService.GetUser(email);
            await this.signInManager.SignInAsync(externalUser, new AuthenticationProperties());

            if (string.IsNullOrEmpty(redirectUrl))
                redirectUrl = "/Home";

            if (!string.IsNullOrEmpty(callback))
            {
                if (!redirectUrl.Contains("?"))
                    redirectUrl += $"?callback={callback}";
                else
                    redirectUrl += $"&callback={callback}";
            }

            return Redirect(redirectUrl);
        }

        /// <summary>
        /// Log off an external user.
        /// </summary>
        /// <param name="callback">Where should the user be taken after being succesfully logged off</param>
        [HttpGet]
        public async Task<IActionResult> Logout(string callback = null)
        {
            await this.HttpContext.SignOutAsync();

            await this.accountService.Logout();

            if (callback != null)
            {
                return Redirect(callback);
            }
            else
            {
                return LocalRedirect("/Identity/Account/Logout");
            }
        }

        /// <summary>
        /// Temporary page displayed before a callback.
        /// </summary>
        [HttpGet]
        public IActionResult Callback()
        {
            return View();
        }

        /// <summary>
        /// Handles automatic login for users redirected from external systems.
        /// </summary>
        /// <param name="email">User email for automatic login</param>
        /// <param name="autoLogin">Flag indicating if automatic login should be attempted</param>
        /// <returns>Redirect to dashboard or login page</returns>
        [HttpGet]
        public async Task<IActionResult> AutoLogin(string email, bool autoLogin = false)
        {
            if (!autoLogin || string.IsNullOrEmpty(email))
            {
                return RedirectToAction("Login", "Account", new { area = "Identity" });
            }

            try
            {
                // Get user by email
                var user = await accountService.GetUser(email);
                if (user == null)
                {
                    // User doesn't exist, redirect to login with error
                    TempData["ErrorMessage"] = localizer["User not found"];
                    return RedirectToAction("Login", "Account", new { area = "Identity" });
                }

                await this.signInManager.SignInAsync(user, new AuthenticationProperties());

                return RedirectToAction("Index", "Home");
            }
            catch (Exception)
            {
                // Log the error and redirect to login
                TempData["ErrorMessage"] = localizer["An error occurred during automatic login"];
                return RedirectToAction("Login", "Account", new { area = "Identity" });
            }
        }
    }
}