﻿using Binit.Framework;
using Binit.Framework.Constants;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers.Email.DTOs;
using Binit.Framework.Helpers.Jwt;
using Binit.Framework.Interfaces.Configuration;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.Email;
using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Domain.Logic.ExternalServices.CompanyRedirection;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using WebAPI.Attributes;
using WebAPI.DTOs.AccountDTOs;
using WebAPI.Helpers;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebAPI.AccountController;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController : ControllerBase
    {
        #region Properties
        private readonly IAccountService accountService;
        private readonly IUserService<BackOfficeUser> backofficeUserService;
        private readonly JWTHelper jwtHelper;
        private readonly IEmailSender emailSender;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IConfiguration configuration;
        private readonly IGeneralConfiguration generalConfiguration;
        private readonly IOperationContext operationContext;
        private readonly IFarmService farmService;
        private readonly ICompanyRedirectionService companyRedirectionService;
        #endregion

        #region Constructor
        public AccountController(IAccountService accountService, IUserService<BackOfficeUser> backofficeUserService,
            JWTHelper jwtHelper, IEmailSender emailSender, IStringLocalizer<SharedResources> localizer,
            IConfiguration configuration, IOperationContext operationContext, IGeneralConfiguration generalConfiguration, IFarmService farmService, ICompanyRedirectionService companyRedirectionService)
        {
            this.accountService = accountService;
            this.backofficeUserService = backofficeUserService;
            this.jwtHelper = jwtHelper;
            this.emailSender = emailSender;
            this.localizer = localizer;
            this.configuration = configuration;
            this.generalConfiguration = generalConfiguration;
            this.operationContext = operationContext;
            this.farmService = farmService;
            this.companyRedirectionService = companyRedirectionService;
        }
        #endregion

        #region Endpoints

        /// <summary>
        /// Returns the available authentication schemes.
		/// Allow anonymous
        /// </summary>
        [HttpGet("authentication-schemes")]
        [AllowAnonymous]
        public async Task<IEnumerable<AuthenticationSchemeRes>> AuthenticationSchemes()
        {
            IList<AuthenticationScheme> authSchemes = await accountService.GetExternalAuthenticationSchemes();
            IEnumerable<AuthenticationSchemeRes> authSchemesDTO = authSchemes.Select(s => new AuthenticationSchemeRes()
            {
                Name = s.Name,
                DisplayName = s.DisplayName
            });

            return authSchemesDTO;
        }

        /// <summary>
        /// Validates the user credentials and returns a valid JWT.
		/// Allow anonymous
        /// </summary>
        /// <param name="login">Login you want to sign in</param>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginReq login)
        {
            await this.HttpContext.SignOutAsync();

            // To ensure a clean login.
            await this.accountService.Logout();

            try
            {
                ApplicationUser user = await accountService.GetFullAsync(login.Email);
                ICollection<string> roles = await accountService.GetRoles(user);

                Microsoft.AspNetCore.Identity.SignInResult result = await accountService.Login(login.Email, login.Password, login.RememberMe);

                if (result.Succeeded)
                {
                    // Check if user should be redirected based on company
                    if (await companyRedirectionService.ShouldRedirectUserAsync(user))
                    {
                        var redirectUrl = companyRedirectionService.GetRedirectUrl();
                        if (!string.IsNullOrEmpty(redirectUrl))
                        {
                            return Ok(new { ShouldRedirect = true, RedirectUrl = redirectUrl });
                        }
                    }

                    // If the login was successful, generate and return the JWT.
                    return await BuildClaimsResponse(user, roles);
                }
                if (result.RequiresTwoFactor)
                {
                    return Forbid(localizer[Lang.LoginRequires2FA]);
                }
                if (result.IsLockedOut)
                {
                    return Unauthorized(localizer[Lang.LoginAccountLocked]);
                }
                else
                {
                    return Unauthorized(localizer[Lang.LoginIncorrectCredentials]);
                }
            }
            catch (ValidationException)
            {
                throw new ValidationException(localizer[Lang.LoginIncorrectCredentials]);
            }
            catch (NotFoundException)
            {
                throw;
            }
            catch (Exception)
            {
                throw new Exception(localizer[Lang.LoginError]);
            }
        }

        /// <summary>
        /// Validates the external user credentials.
        /// Creates (if not exists) or updates the user.
        /// </summary>
        /// <response code="200">Returns a set of tokens to access the API and the Website.</response>   
        /// <response code="401">Missing system user access token on header.</response>   
        /// <response code="403">The access token found on header doesn't belong to a system user (AKA missing the required role).</response>   
        /// <response code="500">There was an error trying to login the external user</response>
        /// <param name="login">External login you want to sign in</param>
        [HttpPost("external/login")]
        [AuthorizeAnyRoles(Roles.BackofficeSystemUser)]
        public async Task<IActionResult> LoginExternal([FromBody] LoginExternalReq login)
        {
            ApplicationUser dbUser = await this.accountService.GetUser(login.Email, asNoTracking: true, onlyActive: false);

            BackOfficeUser userEntity = login.ToEntity();

            // Client user should have the same tenant as the current system user.
            Guid? systemUserTenantId = this.operationContext.GetUserTenantId();
            userEntity.TenantId = systemUserTenantId.Value;
            userEntity.UserType = "backoffice_user";

            if (dbUser == null)
            {
                // Create user if not exists.
                await this.backofficeUserService.CreateAsync(userEntity, login.Roles);
            }
            else
            {
                // Local user id is unknown by the external system buy required to update the user.
                userEntity.Id = dbUser.Id;

                // Update user data if user exists.
                await this.backofficeUserService.UpdateAsync(userEntity, login.Roles);
            }

            // If the login was successful, generate and return the JWT.
            ApplicationUser user = await accountService.GetUser(login.Email);
            string websiteToken = await this.accountService.GenerateAccessToken(user, ApplicationTypes.Website);
            string apiToken = await this.accountService.GenerateAccessToken(user, ApplicationTypes.Api);


            return Ok(new LoginExternalRes()
            {
                WebsiteToken = websiteToken,
                WebsiteTokenExpiration = DateTime.UtcNow.AddMinutes(Convert.ToDouble(configuration.GetSection("JwtSettings")["WebsiteTokenExpirationMinutes"])),
                ApiToken = apiToken,
                ApiRefreshToken = user.RefreshToken
            });
        }

        /// <summary>
        /// Logs out the current user.
        /// </summary>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            await this.HttpContext.SignOutAsync();

            await this.accountService.Logout();

            return Ok();
        }

        /// <summary>
        /// Generates a new access token using the user's refresh token.
        /// </summary>
        /// <param name="refreshModel">Object containing the access and refresh token</param>
        /// <response code="200">New access token successfully generated</response>   
        /// <response code="500">There was an error trying to refresh the access token</response>
        [HttpPost("refresh")]
        [AllowAnonymous]
        public async Task<IActionResult> Refresh(RefreshReq refreshModel)
        {
            var newJwtToken = await this.accountService.RefreshToken(refreshModel.AccessToken, refreshModel.RefreshToken);

            return Ok(newJwtToken);
        }


        /// <summary>
        /// Creates a new user with password and sends the Welcome email with a confirmation code.
		/// Allow anonymous
        /// </summary>
        /// <param name="register">Register you want to make to create a new user with password</param>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegisterReq register)
        {
            try
            {
                Domain.Entities.Model.ApplicationUser user = register.ToEntity();
                Domain.Entities.Model.ApplicationUser existentUser = await accountService.GetUser(user.Email, onlyActive: false);
                if (existentUser != null)
                {
                    // Don't let a potential attacker know if a user is already registered.
                    return Ok();
                }

                string code = await accountService.Register(user, register.Password);

                Dictionary<string, string> queryParams = new Dictionary<string, string>();
                queryParams.Add("userId", user.Id.ToString());
                queryParams.Add("code", code);

                string callbackUrl = QueryHelpers.AddQueryString(register.ConfirmEmailCallback, queryParams);

                await emailSender.SendEmailAsync(user.Email, this.localizer[Lang.RegisterEmailSubject],
                    new WelcomeDTO(generalConfiguration, localizer) { Name = user.Email, CallbackUrl = callbackUrl });

                return Ok();
            }
            catch (IdentityException idex)
            {
                // Throw new ValidationException to be handled by the ExceptionHandlerMiddleware.
                throw new ValidationException(idex.Message, idex);
            }
        }

        /// <summary>
        /// Confirms the user's email.
        /// This endpoint should be called when a user received the Welcome email and clicked on the link.
		/// Allow anonymous
        /// </summary>
        /// <param name="confirmEmail">Email you want to confirm</param>
        [HttpPost("confirm-email")]
        [AllowAnonymous]
        public async Task<IActionResult> ConfirmEmail([FromBody] ConfirmEmailReq confirmEmail)
        {
            try
            {
                await this.accountService.ConfirmEmail(confirmEmail.UserId, confirmEmail.Code);
                return Ok();
            }
            catch (NotFoundException)
            {
                // Don't reveal that the user does not exist.
                return Ok();
            }
            catch (IdentityException idex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                     idex.Errors.Select(ie => new { ie.Code, ie.Description })
                     .ToDictionary(di => di.Code, di => new string[] { di.Description })
                 );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Sends the Forgot password email with a recovery code.
		/// Allow anonymous
        /// </summary>
        /// <param name="forgotPassword">Password you forgot</param>
        [HttpPost("forgot-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordReq forgotPassword)
        {
            try
            {
                string code = await accountService.GeneratePasswordToken(forgotPassword.Email);

                Dictionary<string, string> queryParams = new Dictionary<string, string>();
                queryParams.Add("code", code);

                string callbackUrl = QueryHelpers.AddQueryString(forgotPassword.ForgotPasswordEmailCallback, queryParams);

                await emailSender.SendEmailAsync(forgotPassword.Email, this.localizer[Lang.ForgotPasswordEmailSubject],
                new ForgotPasswordDTO(generalConfiguration, localizer) { Name = forgotPassword.Email, CallbackUrl = callbackUrl });

                return Ok();
            }
            catch (NotFoundException)
            {
                // Don't reveal that the user does not exist.
                return Ok();
            }
        }

        /// <summary>
        /// Resets the user's password.
        /// This endpoint should be called when a user received the Forgot password email and clicked on the link.
		/// Allow anonymous
        /// </summary>
        /// <param name="resetPassword">Password you want to reset</param>
        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordReq resetPassword)
        {
            try
            {
                await this.accountService.ResetPassword(resetPassword.Email, resetPassword.Code, resetPassword.Password);

                return Ok();
            }
            catch (NotFoundException)
            {
                // Don't reveal that the user does not exist.
                return Ok();
            }
            catch (IdentityException idex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                    idex.Errors.Select(ie => new { ie.Code, ie.Description })
                    .ToDictionary(di => di.Code, di => new string[] { di.Description })
                );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Changes the user's password.
        /// This endpoint should be called by an authorized user logged in with a manual account (user and password)
        /// who wants to change their current password.
        /// </summary>
        /// <param name="changePasswordReq">Password you want to change</param>
        [HttpPut("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordReq changePasswordReq)
        {
            try
            {
                await this.accountService.ChangePassword(changePasswordReq.OldPassword, changePasswordReq.NewPassword);

                return Ok();
            }
            catch (IdentityException idex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                   idex.Errors.Select(ie => new { ie.Code, ie.Description })
                   .ToDictionary(di => di.Code, di => new string[] { di.Description })
               );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Sets the user's first password.
        /// This endpoint should be called by an authorized user that in with a social account 
        /// who wants to allow manual login (with user and password).
        /// </summary>
        /// <param name="setPasswordReq">Password you want to set for the user</param>
        [HttpPut("set-password")]
        [Authorize]
        public async Task<IActionResult> SetPassword([FromBody] SetPasswordReq setPasswordReq)
        {
            try
            {
                await this.accountService.SetPassword(setPasswordReq.NewPassword);

                return Ok();
            }
            catch (IdentityException idex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                     idex.Errors.Select(ie => new { ie.Code, ie.Description })
                     .ToDictionary(di => di.Code, di => new string[] { di.Description })
                 );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Confirms user email and sets their first password.
        /// This endpoint should be called when the user confirming their email was created by another user.
        /// </summary>
        /// <param name="createPasswordReq">Password you want to create</param>
        [HttpPost("create-password")]
        public async Task<IActionResult> CreatePassword([FromBody] CreatePasswordReq createPasswordReq)
        {
            try
            {
                await this.accountService.CreatePassword(createPasswordReq.UserId, createPasswordReq.Code, createPasswordReq.NewPassword);

                return Ok();
            }
            catch (IdentityException idex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                     idex.Errors.Select(ie => new { ie.Code, ie.Description })
                     .ToDictionary(di => di.Code, di => new string[] { di.Description })
                 );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Do a challenge of social authentication with the provider passed by param
        /// </summary>
        /// <param name="provider">Provider which you want to do a challenge of social authentication</param>
        [HttpGet("social-authentication")]
        [AllowAnonymous]
        public IActionResult SocialAuthentication(string provider)
        {
            string externalLoginHandler = Url.Action(nameof(HandleExternalLogin));
            AuthenticationProperties authenticationProperties = this.accountService.GetExternalAuthenticationProperties(provider, externalLoginHandler);
            return Challenge(authenticationProperties, provider);
        }

        /// <summary>
        /// Handle challenge response
        /// </summary>
        [HttpGet("handle-external-login")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleExternalLogin()
        {
            ExternalLoginInfo externalLoginInfo = await this.accountService.GetExternalLoginInfoAsync();
            Domain.Entities.Model.ApplicationUser externalLoginUser = await this.accountService.FindAsync(externalLoginInfo);

            string deeplink = "";
            if (externalLoginUser == null)
            {
                deeplink = DeeplinksHelper.GetDeeplinkForCompleteInformation(this.configuration, externalLoginInfo);
            }
            else
            {
                string accessToken = await this.accountService.GenerateAccessToken(externalLoginUser, ApplicationTypes.Api);
                deeplink = $"{DeeplinksHelper.GetURL(this.configuration)}/authorize-access/{accessToken}";
            }

            return Redirect(deeplink);
        }

        /// <summary>
        /// Completes user's social registration
        /// </summary>
        /// <param name="completeSocialAuthReq">Social authentication you want to complete</param>
        [HttpPost("complete-social-auth")]
        [AllowAnonymous]
        public async Task<IActionResult> CompleteSocialAuth([FromBody] CompleteSocialAuthReq completeSocialAuthReq)
        {
            Domain.Entities.Model.ApplicationUser user = completeSocialAuthReq.ToEntity();
            ExternalLoginInfo info = new ExternalLoginInfo(
                principal: new ClaimsPrincipal(),
                loginProvider: completeSocialAuthReq.LoginProvider,
                providerKey: completeSocialAuthReq.ProviderKey,
                displayName: completeSocialAuthReq.LoginProvider
            );
            bool canSignIn = await this.accountService.CanSignInAsync(user, info);

            if (canSignIn)
            {
                user = await accountService.FindAsync(info);
                string accessToken = await this.accountService.GenerateAccessToken(user);

                return Ok(new LoginRes()
                {
                    Token = accessToken
                });
            }
            else
            {
                return StatusCode(500, this.localizer[Lang.LoginError]);
            }
        }
        #endregion

        #region Private Methods 
        private async Task<IActionResult> BuildClaimsResponse(ApplicationUser user, ICollection<string> roles, string companyId = null, string sectorId = null)
        {
            (Claim tenantClaim, List<Claim> companyClaims, List<Claim> sectorsClaims) = await BuildFullClaims(user, companyId, sectorId);

            DateTime apiTokenExpiration = DateTime.UtcNow.AddMinutes(Convert.ToDouble(configuration.GetSection("JwtSettings")["ApiTokenExpirationMinutes"]));

            return Ok(new LoginRes()
            {
                Token = jwtHelper.GenerateToken(user, roles, tenantClaim, companyClaims, sectorsClaims, apiTokenExpiration),
                RefreshToken = user.RefreshToken
            });
        }

        /// <summary>
        /// Build lists for each claim type
        /// </summary>
        private async Task<Tuple<Claim, List<Claim>, List<Claim>>> BuildFullClaims(ApplicationUser user, string companyId = null, string sectorId = null)
        {
            // Tenant claim
            Claim tenantClaim = await accountService.GetTenantClaim(user);

            // Company claims
            List<Claim> companyClaims = new List<Claim>();
            string companies = null;

            if (user.Companies != null)
                companies = string.Join(",", user.Companies.Select(c => c.CompanyId));

            if (!string.IsNullOrEmpty(companies))
                companyClaims.Add(new Claim(CustomClaimTypes.CurrentCompany, companies));

            // Site claims
            List<Claim> siteClaims = new List<Claim>();
            string sites = null;

            if (user.Sites != null)
                sites = string.Join(",", user.Sites.Select(c => c.SiteId));

            if (!string.IsNullOrEmpty(sites))
                companyClaims.Add(new Claim(CustomClaimTypes.CurrentSite, sites));

            return new Tuple<Claim, List<Claim>, List<Claim>>(tenantClaim, companyClaims, siteClaims);
        }
        #endregion
    }
}
