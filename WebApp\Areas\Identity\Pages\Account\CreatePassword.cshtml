﻿@page
@model CreatePasswordModel
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.CreatePassword
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@{
    ViewData["Title"] = localizer[Lang.Title];
    Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<section id="wrapper" class="login-register login-sidebar" style="background-image:url('@Url.Content("~/images/bg-register.png")');">
    <div class="login-box card">
        <div class="card-body">
            <form method="post" class="floating-labels" >
                <a href="javascript:void(0)" class="text-center db">
                    <img src="@Url.Action("Display", "Tenant", new { replace = "logo-black.png" })" class="mb-2" width="200" alt="Home" />
                </a>
                <h3 class="box-title m-t-40">@ViewData["Title"]</h3>
                
                <!-- Password -->
                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group m-b-40">
                    <div class="col-xs-12">               
                        <input asp-for="Input.Password" class="form-control" type="password" required>
                        <span class="bar"></span>
                        <label asp-for="Input.Password"></label>
                        <span asp-validation-for="Input.Password" class="form-control-feedback"></span>                        
                    </div>
                </div>

                <!-- ConfirmPassword -->
                <div class="form-group">
                    <div class="col-xs-12">
                        <input asp-for="Input.ConfirmPassword" class="form-control" type="password" required>
                        <span class="bar"></span>
                        <label asp-for="Input.ConfirmPassword"></label>
                        <span asp-validation-for="Input.ConfirmPassword" class="form-control-feedback"></span>                        
                    </div>
                </div>

                <div class="form-group text-center m-t-20">
                    <div class="col-xs-12">
                        <button class="btn btn-themecolor btn-lg btn-block text-uppercase btn-rounded" type="submit">@localizer[Lang.SubmitButton]</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>
