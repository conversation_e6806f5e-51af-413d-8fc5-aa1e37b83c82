.vtabs {
    display: table; }
    .vtabs .tabs-vertical {
      
      border-bottom: 0px;
      border-right: 1px solid rgba(120, 130, 140, 0.13);
      display: table-cell;
      vertical-align: top; }
      .vtabs .tabs-vertical li .nav-link {
        color: #263238;
        margin-bottom: 10px;
        border: 0px;
        border-radius: 4px 0 0 4px; }
    .vtabs .tab-content {
      display: table-cell;
      padding: 20px;
      vertical-align: top; }
  
  .tabs-vertical li .nav-link.active,
  .tabs-vertical li .nav-link:hover,
  .tabs-vertical li .nav-link.active:focus {
    background: #f07a22;
    border: 0px;
    color: #ffffff; }
  
  /*Custom vertical tab*/
  .customvtab .tabs-vertical li .nav-link.active,
  .customvtab .tabs-vertical li .nav-link:hover,
  .customvtab .tabs-vertical li .nav-link:focus {
    background: #ffffff;
    border: 0px;
    border-right: 2px solid #f07a22;
    margin-right: -1px;
    color: #f07a22; }
  
  .tabcontent-border {
    border: 1px solid #ddd;
    border-top: 0px; }
  
  .customtab2 li a.nav-link {
    border: 0px;
    margin-right: 3px;
    color: black; }
    .customtab2 li a.nav-link.active {
      background: #f07a22;
      color: #ffffff; }
    .customtab2 li a.nav-link:hover {
      color: #ffffff;
      background: #f07a22; }