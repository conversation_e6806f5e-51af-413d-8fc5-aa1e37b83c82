@model WebApp.Models.BestPracticeViewModel;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.BestPractice.CreateOrEdit
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer

@{
    int i = (int)ViewData["Index"];
    var editable = (bool)ViewData["Editable"];
    var frequencyCheckList = ViewData["FrequencyCheckList"] as List<SelectListItem>;
    var farmUserRolesEnum = ViewData["FarmUserRolesEnum"] as List<SelectListItem>;
    var relevances = ViewData["Relevances"] as List<SelectListItem>;
    var taskTypes = ViewData["TaskTypes"] as List<SelectListItem>;
    var timePeriodTypes = ViewData["TimePeriodTypes"] as List<SelectListItem>;
    if (timePeriodTypes == null)
        timePeriodTypes = new List<SelectListItem>();
    var weekDays = ViewData["WeekDays"] as List<SelectListItem>;
    var taskTypeClass = "other-task";
    var imageURL = "/images/views/bestPractice/other.png";
    if (Model.BestPracticeTasks[i].TaskType == "HealthCare")
    {
        taskTypeClass = "health-care-task";
        imageURL = "/images/views/bestPractice/healthCare.png";
    }
    else if (Model.BestPracticeTasks[i].TaskType == "WellBeing")
    {
        taskTypeClass = "well-being-task";
        imageURL = "/images/views/bestPractice/wellBeing.png";
    }
    else if (Model.BestPracticeTasks[i].TaskType == "Maintenance")
    {
        taskTypeClass = "maintenance-task";
        imageURL = "/images/views/bestPractice/maintenance.png";
    }
}

    <div id="best-practice-task-@i" class="best-practice-task p-3">
        <input type="hidden" asp-for="BestPracticeTasks[i].Id" />
        <input type="hidden" asp-for="BestPracticeTasks[i].AlarmDate" />
        <input type="hidden" asp-for="BestPracticeTasks[i].ExpirationDate" />
        <input type="hidden" asp-for="BestPracticeTasks[i].Excluded" />
        <div class="row align-items-baseline" style="padding-right: 15px">
            <div class="col-md-2 taskIcon @taskTypeClass" style="text-align:center">
                <img src="@imageURL" style="max-height:10rem" />
            </div>
            <div class="col-md-4">
                <ignite-input for-property="BestPracticeTasks[i].Name" type="text" class="col-md-10"></ignite-input>
            </div>
            <div class="col-md-3">
                <ignite-dropdown class="select2-tasktype" for-property="BestPracticeTasks[i].TaskType"
                                 items="@taskTypes"
                                 placeholder="@localizer[Lang.PlaceholderSelectTaskType]">
                </ignite-dropdown>
            </div>
            <div class="col-md-3">
                <ignite-dropdown class="select2-relevance" for-property="BestPracticeTasks[i].Relevance"
                                 items="@relevances"
                                 placeholder="@localizer[Lang.PlaceholderSelectRelevance]">
                </ignite-dropdown>
            </div>
        </div>

        <div class="col-md-12">
            <ignite-textarea for-property="BestPracticeTasks[i].Description"></ignite-textarea>
        </div>

        <div style="padding-left:15px; padding-right:15px">
            @if (Model.EntityType == "Genetic" || Model.EntityType == "HenBatch")
            {
                <div class="row col-md-4">
                    <h6 id="spanLabel-@i">@localizer[Lang.LabelSpanHenBatch]</h6>
                </div>

                <div class="row align-items-end">
                    <div id="spanContainer-@i" class="col-md-4">
                        <ignite-input for-property="BestPracticeTasks[i].SpanValue" type="number"></ignite-input>
                    </div>
                    <div class="col-md-4">
                        <ignite-dropdown for-property="BestPracticeTasks[i].SpanTimePeriodType"
                                         items="@timePeriodTypes">
                        </ignite-dropdown>
                    </div>
                    <div id="weekContainer-@i" class="m-b-10" style="display:@(Model.BestPracticeTasks[i].SpanTimePeriodType == "Month" ? "" : "none")">
                        @for (int wd = 0; wd < weekDays.Count; wd++)
                        {
                            <a value="@weekDays[wd].Value" class="btn weekDay-@i btn-outline-inverse @(Model.BestPracticeTasks[i].WeekDay.ToString() == weekDays[wd].Value ? "btn-themecolor" : "")">
                                @weekDays[wd].Text
                            </a>
                        }
                    </div>
                    <input type="hidden" asp-for="BestPracticeTasks[i].WeekDay" />
                </div>
            }
            else //If it's assigned to other type of containers, the starting date is indicated as from the assignment date
            {
                <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.LabelSpan]</h6>
                <div id="spanContainer-@i" class="floating-labels row align-content-center">

                    <ignite-input for-property="BestPracticeTasks[i].SpanValue" type="number"></ignite-input>

                    <div class="col-4">
                        <ignite-dropdown for-property="BestPracticeTasks[i].SpanTimePeriodType"
                                         items="@timePeriodTypes">
                        </ignite-dropdown>
                    </div>
                    <div id="weekContainer-@i" class="m-b-10" style="display:@(Model.BestPracticeTasks[i].SpanTimePeriodType == "Month" ? "" : "none")">
                        @for (int wd = 0; wd < weekDays.Count; wd++)
                        {
                            <a value="@weekDays[wd].Value" class="btn weekDay-@i btn-outline-inverse @(Model.BestPracticeTasks[i].WeekDay.ToString() == weekDays[wd].Value ? "btn-themecolor" : "")">
                                @weekDays[wd].Text
                            </a>
                        }
                    </div>
                    <input type="hidden" asp-for="BestPracticeTasks[i].WeekDay" />
                </div>
            }
                    <div class="row align-items-end">
                        <div class="col-md-4 col-sm-6">
                            <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.DurationLabel]</h6>
                            <ignite-input for-property="BestPracticeTasks[i].Duration" type="number"></ignite-input>
                        </div>

                        <div class="col-md-4  col-sm-6">
                            <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.AlertSpanValueLabel]</h6>
                            <ignite-input for-property="BestPracticeTasks[i].AlertSpanValue" type="number"></ignite-input>
                        </div>
                    </div>

                </div>
        <div style="width:100%; margin:auto">
            <div id="frequenciesContainer-@i" class="frequenciesContainer">
                @{
                    int iF = 0;
                    foreach (var f in Model.BestPracticeTasks[i].Frequencies)
                    {
                        @await Component.InvokeAsync("FrequencyRow", new { indexTask = i, index = iF, model = Model, timePeriodTypes = timePeriodTypes, weekDays = weekDays });
                        iF++;
                    }
                }
            </div>
            <div>
                <button id="add-frequency-@i" type="button" class="btn btn-outline-inverse addFrequency" style="justify-content:end">
                    <i class="fa fa-plus"></i> @(localizer[Lang.BtnAddFrequency])
                </button>
            </div>
        </div>

        <div class="notifyByEmail" style="margin-left:1rem;">
            <ignite-checkbox for-property="BestPracticeTasks[i].NotifyByEmail" onchange="handleNotificationByEmailChange(@i)"></ignite-checkbox>
        </div>
        <div class="row pl-3" id="infoEmailNotification">
            <div class="col-5 email-input-@i p-0" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <div class="btn-group col-md-5 pr-0 btn-alarmDate" aria-label="date" role="group" style="margin-top:17px">
                    <button type="button" id="btn-alarmDate-@i" class="btn @(Model.BestPracticeTasks[i].AlarmDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.BestPracticeTasks[i].AlarmDate ? "border-color:#f07a22a1":"")" onclick="changesColorAlarm(@i);">@localizer[Lang.BtnAlarmLabel]</button>
                </div>
                <div class="btn-group col-md-5 pl-0 btn-expirationDate" aria-label="date" role="group" style="margin-top:17px">
                    <button type="button" id="btn-expirationDate-@i" class="btn @(Model.BestPracticeTasks[i].ExpirationDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.BestPracticeTasks[i].ExpirationDate ? "border-color:#f07a22a1":"")" onclick="changesColorExpiration(@i);">@localizer[Lang.BtnExpirationLabel]</button>
                </div>
            </div>

            <div class="col-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <ignite-dropdown for-property="BestPracticeTasks[i].FrequencyCheck"
                                 items="@frequencyCheckList"
                                 placeholder="@localizer[Lang.PlaceholderEmailNotificationFrequency]">
                </ignite-dropdown>
            </div>

            <div class="col-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <ignite-input for-property="BestPracticeTasks[i].NotificationAmount" type="text"></ignite-input>
            </div>
            
            <div class="col-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <ignite-dropdown for-property="BestPracticeTasks[i].FarmUserRoles" items="@farmUserRolesEnum " multiple="true"></ignite-dropdown>
            </div>
        </div>

        <div>
            <div class="m-b-10 m-t-10" style="width:100%; text-align:center; align-items:center">
                @if (editable)
                {
                    <a name="@i" class="btn btn-green reduceTask m-r-10"
                       style="vertical-align:middle; color:white">
                        <i class="fas fa-chevron-up" style="font-size:20px"></i>
                        @localizer[Lang.ReduceTaskLabel]
                    </a>
                    <a name="@i" class="btn btn-danger deleteTask m-r-10"
                       style="vertical-align:middle; color:white">
                        <i class="fas fa-trash" style="font-size:20px"></i>
                        @localizer[Lang.DeleteTaskLabel]
                    </a>
                }
                <button type="button" onclick="OpenCurrentModal(@i)" class="btn btn-success"><i class="fa fa-file"></i>@localizer[Lang.FileButton]</button>
            </div>
        </div>
    </div>

<div id="best-practice-task-@i-reduced" name="@i" style="width:100%; display:none" class="m-b-15 m-t-10 best-practice-task-reduced">
    <div class="p-10 row showTask">
        <div class="col-md-md-1 taskIcon @taskTypeClass">
            <img src="@imageURL" style="max-height:10rem" />
        </div>
        <div id="taskName-@i" class="col-md-md-9" style="margin:auto">
            @localizer[Lang.NoName]
        </div>
        <div class="d-flex justify-content-end col-md-md-2" style="margin:auto">
            <a style="vertical-align:middle">
                <i class="fas fa-chevron-down" style="font-size:30px"></i>
            </a>
        </div>
    </div>
</div>
