using Binit.Framework;
using Binit.Framework.Helpers.Configuration;
using Binit.Framework.Interfaces.Configuration;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using ModelErrorMessages = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.ErrorMessages;

namespace WebApp.Middleware
{
    /// <summary>
    /// Extension methods for setting up MVC services in the ServiceCollection using Ignite's configurations.
    /// </summary>
    public static class IgniteMvcExtensions
    {
        /// <summary>
        /// Adds MVC services using Ignite's configurations.
        /// </summary>
        public static IMvcBuilder AddIgniteMvc(this IServiceCollection services, IConfiguration configuration)
        {
            return services.AddMvc(options =>
            {
#pragma warning disable ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'
                var localizer = services.BuildServiceProvider().GetService<IStringLocalizer<SharedResources>>();
#pragma warning restore ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'

                options.ModelBindingMessageProvider.SetValueMustBeANumberAccessor((x) => string.Format(localizer[ModelErrorMessages.ValueMustBeANumberAccessor], x));

                IGeneralConfiguration generalConfiguration = new GeneralConfiguration(configuration);

                if (generalConfiguration.EnableAntiforgeryToken)
                    options.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
                else
                    options.Filters.Add(new IgnoreAntiforgeryTokenAttribute());
            });
        }

        /// <summary>
        /// Adds the company redirection middleware to the pipeline.
        /// </summary>
        public static IApplicationBuilder UseCompanyRedirection(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<CompanyRedirectionMiddleware>();
        }
    }
}