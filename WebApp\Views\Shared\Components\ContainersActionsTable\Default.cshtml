﻿@model WebApp.Models.AbstractContainerViewModel;
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Shared.Components.ContainerProperties;
@using Domain.Entities.Model.Enum;
@using Binit.Framework.Constants.SeedEntities;
@{
    var farms = ViewData["farms"] as List<SelectListItem>;
    var areas = ViewData["areas"] as List<SelectListItem>;
    var containers = ViewData["CapacityUnits"] as List<SelectListItem>;
    var action = ViewData["Action"] as string;
    var name = ViewData["Name"] as string;
}

<input type="hidden" asp-for="ContainerProperties.Id" />
<input type="hidden" asp-for="Id" />

@if (action != "Details")
{
    @if (!string.Equals(name, EntityTypeEnum.Line.ToString(), StringComparison.OrdinalIgnoreCase))
    {
        @if (!string.Equals(name, EntityTypeEnum.HenWarehouse.ToString(), StringComparison.OrdinalIgnoreCase) && !string.Equals(name, EntityTypeEnum.Silo.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            <div class="row align-items-end">
                @if (string.Equals(name, EntityTypeEnum.PackingWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
               || string.Equals(name, EntityTypeEnum.FeedFactory.ToString(), StringComparison.OrdinalIgnoreCase)
               || string.Equals(name, EntityTypeEnum.PreparationWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
               || string.Equals(name, EntityTypeEnum.ClassificationWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
               || string.Equals(name, EntityTypeEnum.StorageWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
               || string.Equals(name, ContainerTypes.DispositionWarehouse, StringComparison.OrdinalIgnoreCase))
                {
                    <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 col-12">
                        <ignite-input for-property="@Model.Name" type="text"></ignite-input>
                    </div>
                }
                else
                {
                    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                        <ignite-input for-property="ContainerProperties.Name" type="text"></ignite-input>
                    </div>
                }

                @if (!(string.Equals(name, EntityTypeEnum.StorageWarehouse.ToString(), StringComparison.OrdinalIgnoreCase) && action == "Create"))
                {
                    <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 col-12">
                        <ignite-input for-property="ContainerProperties.Code" type="text"></ignite-input>
                    </div>
                }

                <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 col-12">
                    <ignite-dropdown for-property="ContainerProperties.FarmId"
                                     items="@farms"
                                     placeholder="@localizer[Lang.PlaceholderSelectFarm]">
                    </ignite-dropdown>
                </div>

            </div>
        }
        @if (!string.Equals(name, EntityTypeEnum.PackingWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
           && !string.Equals(name, EntityTypeEnum.PreparationWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
           && !string.Equals(name, EntityTypeEnum.Slaughterhouse.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            <div class="row align-items-end">
                @if (action == "Edit")
                {
                    <div class="floating-labels col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" style="display:flex">
                        @if (!string.Equals(name, EntityTypeEnum.HenWarehouse.ToString(), StringComparison.OrdinalIgnoreCase) || !string.Equals(name, EntityTypeEnum.Silo.ToString(), StringComparison.OrdinalIgnoreCase))
                        {
                            if (!string.Equals(name, EntityTypeEnum.ClassificationWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
                                && !string.Equals(name, EntityTypeEnum.StorageWarehouse.ToString(), StringComparison.OrdinalIgnoreCase)
                                && !string.Equals(name, EntityTypeEnum.FeedFactory.ToString(), StringComparison.OrdinalIgnoreCase)
                                && !string.Equals(name, ContainerTypes.DispositionWarehouse, StringComparison.OrdinalIgnoreCase)
                                && !string.Equals(name, EntityTypeEnum.Silo.ToString(), StringComparison.OrdinalIgnoreCase))
                            {
                                <div class="col-3 p-0">
                                    <ignite-input for-property="ContainerProperties.Name" type="text"></ignite-input>
                                </div>
                            }
                            else if (string.Equals(name, EntityTypeEnum.Silo.ToString(), StringComparison.OrdinalIgnoreCase))
                            {
                                <div class="col-3 p-0">
                                    <ignite-input for-property="ContainerProperties.Name" type="text"></ignite-input>
                                </div>
                            }

                            <div class="col-6">
                                <ignite-input for-property="ContainerProperties.DetailedName" type="text"
                                              disabled="@(!Model.ContainerProperties.ModifiedDetailedName)"></ignite-input>
                            </div>
                        }
                        else
                        {
                            <div class="col-9 p-0">
                                <ignite-input for-property="ContainerProperties.DetailedName" type="text"
                                              disabled="@(!Model.ContainerProperties.ModifiedDetailedName)"></ignite-input>
                            </div>
                        }

                        <div class="col-3">
                            <ignite-checkbox for-property="ContainerProperties.ModifiedDetailedName" color="#f07a22"></ignite-checkbox>
                        </div>

                        @if (name == "henWarehouse" || name == "Silo")
                        {
                            <div class="col-4">
                                <ignite-input for-property="ContainerProperties.Code" type="text"></ignite-input>
                            </div>
                        }
                    </div>
                }
            </div>
        }

    }

    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            @if (!name.Equals(EntityTypeEnum.Line.ToString()))
            {
                <ignite-dropdown for-property="ContainerProperties.AreaEnums"
                                 items="@areas"
                                 multiple>
                </ignite-dropdown>
            }
            else
            {
                <ignite-dropdown for-property="ContainerProperties.AreaEnums"
                                 items="@areas"
                                 multiple disabled>
                </ignite-dropdown>
            }
        </div>

        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="@Model.ContainerProperties.OriginContainersId"
                             items="@containers"
                             multiple="true"
                             search-url="@Url.Action("SearchOrigin","StorageWarehouse")">
            </ignite-dropdown>
        </div>
    </div>
}
else
{
    @if (name != EntityTypeEnum.HenWarehouse.ToString() && name != EntityTypeEnum.Silo.ToString())
    {
        <div class="row align-items-end">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <ignite-input for-property="ContainerProperties.Name" type="text" disabled></ignite-input>
            </div>

            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <ignite-dropdown for-property="ContainerProperties.FarmId"
                                 items="@Model.ContainerProperties.Farms" disabled>
                </ignite-dropdown>
            </div>
        </div>
    }

    <div class="row align-items-end">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="ContainerProperties.AreaEnums"
                             multiple="true"
                             items="@Model.ContainerProperties.AreasEnum" disabled>
            </ignite-dropdown>
        </div>

        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <ignite-dropdown for-property="@Model.ContainerProperties.OriginContainersId"
                             multiple="true"
                             items="@Model.ContainerProperties.OriginContainers" disabled>
            </ignite-dropdown>
        </div>
    </div>
}