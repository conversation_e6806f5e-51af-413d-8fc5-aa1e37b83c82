﻿@page
@model ResetPasswordModel
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.ResetPassword
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@{
    ViewData["Title"] = localizer[Lang.Title];
	Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<section id="wrapper" class="login-register login-sidebar" style="background-image:url('@Url.Content("~/images/bg-register.png")');">
    <div class="login-box card">
        <div class="card-body">
            <form method="post" class="floating-labels" >
                <a href="javascript:void(0)" class="text-center db">
                    <img src="@Url.Action("Display", "Tenant", new { replace = "logo-black.png" })" class="mb-2" width="200" alt="Home" />
                </a>
                <h3 class="box-title m-t-40">@ViewData["Title"]</h3>
                <small class="mb-4">@localizer[Lang.Subtitle]</small>
                <div asp-validation-summary="All" class="text-danger"></div>
                
				<input asp-for="Input.Code" type="hidden" />
            
				<!-- Email -->
				<div class="form-group m-t-40 m-b-40">
					<input asp-for="Input.Email" class="form-control" />
					<span class="bar"></span>                
					<label asp-for="Input.Email"></label>
					<span asp-validation-for="Input.Email" class="text-danger"></span>
				</div>
				<!-- Password -->
				<div class="form-group m-b-40">
					<input asp-for="Input.Password" class="form-control" />
					<span class="bar"></span>                   
					<label asp-for="Input.Password"></label>
					<span asp-validation-for="Input.Password" class="text-danger"></span>
				</div>
				<!-- Confirm Password -->
				<div class="form-group">
					<input asp-for="Input.ConfirmPassword" class="form-control" />
					<span class="bar"></span>                
					<label asp-for="Input.ConfirmPassword"></label>
					<span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
				</div>
                <div class="form-group text-center m-t-20">
                    <div class="col-xs-12">
                        <button class="btn btn-themecolor btn-lg btn-block text-uppercase btn-rounded" type="submit">@localizer[Lang.SubmitButton]</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}