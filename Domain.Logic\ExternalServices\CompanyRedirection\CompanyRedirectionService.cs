using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.ExternalServices.CompanyRedirection
{
    public class CompanyRedirectionService : ICompanyRedirectionService
    {
        private readonly IAccountService _accountService;

        public CompanyRedirectionService(IAccountService accountService)
        {
            _accountService = accountService;
        }

        public string GetRedirectUrl()
        {
            return IsRedirectionEnabled() ? "https://pmbbrovah-dev-app-web-br01.azurewebsites.net/" : null;
        }

        public bool IsRedirectionEnabled()
        {
            return true;
        }

        private List<Guid> GetCompanyIdsToRedirect()
        {
            var companyIds = new List<Guid>
            {
                Guid.Parse("F72032FE-527F-1486-7FC3-39FFF89E1076"),
                Guid.Parse("684A5022-318C-57E9-7A98-39FFF89ECCC7"),
                Guid.Parse("0BC8781E-2EC5-B5C2-93C1-39FFF8A0EBE4"),
                Guid.Parse("FADF2E51-06C5-4A07-86BB-08D92C3DD4DB")
            };

            return companyIds;
        }
        public Task<bool> ShouldRedirectUserAsync(ApplicationUser user)
        {
            if (!IsRedirectionEnabled() || user?.Companies == null || !user.Companies.Any())
                return Task.FromResult(false);

            // Only redirect if user has exactly one company associated
            if (user.Companies.Count != 1)
                return Task.FromResult(false);

            var companyIdsToRedirect = GetCompanyIdsToRedirect();
            if (!companyIdsToRedirect.Any())
                return Task.FromResult(false);

            // Check if the user's single company is in the redirect list
            var userCompanyId = user.Companies.First().CompanyId;
            return Task.FromResult(companyIdsToRedirect.Contains(userCompanyId));
        }

        public async Task<bool> ShouldRedirectUserAsync(string email)
        {
            if (!IsRedirectionEnabled() || string.IsNullOrEmpty(email))
                return false;

            try
            {
                // Get user with company information
                var user = await _accountService.GetFullAsync(email);
                return await ShouldRedirectUserAsync(user);
            }
            catch
            {
                // If user doesn't exist or any error occurs, don't redirect
                return false;
            }
        }
    }
}
