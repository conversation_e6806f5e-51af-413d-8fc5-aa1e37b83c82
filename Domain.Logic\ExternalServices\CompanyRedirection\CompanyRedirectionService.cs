using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.ExternalServices.CompanyRedirection
{
    public class CompanyRedirectionService : ICompanyRedirectionService
    {
        private readonly CompanyRedirectionConfiguration _configuration;
        private readonly IAccountService _accountService;
        private readonly ICompanyService _companyService;

        public CompanyRedirectionService(
            CompanyRedirectionConfiguration configuration,
            IAccountService accountService,
            ICompanyService companyService)
        {
            _configuration = configuration;
            _accountService = accountService;
            _companyService = companyService;
        }

        public async Task<bool> ShouldRedirectUserAsync(ApplicationUser user)
        {
            if (!IsRedirectionEnabled() || user?.Companies == null || !user.Companies.Any())
                return false;

            var companyIdsToRedirect = GetCompanyIdsToRedirect();
            if (!companyIdsToRedirect.Any())
                return false;

            // Check if any of the user's companies are in the redirect list
            var userCompanyIds = user.Companies.Select(cu => cu.CompanyId).ToList();
            return userCompanyIds.Any(companyId => companyIdsToRedirect.Contains(companyId));
        }

        public async Task<bool> ShouldRedirectUserAsync(string email)
        {
            if (!IsRedirectionEnabled() || string.IsNullOrEmpty(email))
                return false;

            try
            {
                // Get user with company information
                var user = await _accountService.GetFullAsync(email);
                return await ShouldRedirectUserAsync(user);
            }
            catch
            {
                // If user doesn't exist or any error occurs, don't redirect
                return false;
            }
        }

        public string GetRedirectUrl()
        {
            return IsRedirectionEnabled() ? _configuration.RedirectUrl : null;
        }

        public bool IsRedirectionEnabled()
        {
            return _configuration.IsEnabled && !string.IsNullOrEmpty(_configuration.RedirectUrl);
        }

        private List<Guid> GetCompanyIdsToRedirect()
        {
            var companyIds = new List<Guid>();
            
            if (_configuration.CompanyIdsToRedirect != null)
            {
                foreach (var companyIdString in _configuration.CompanyIdsToRedirect)
                {
                    if (Guid.TryParse(companyIdString, out var companyId))
                    {
                        companyIds.Add(companyId);
                    }
                }
            }

            return companyIds;
        }
    }
}
