@model WebApp.Models.MovementReportViewModel;
@using Binit.Framework
@using Microsoft.Extensions.Localization
@using Domain.Entities.Model.Enum
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.MovementReport.CreateOrEdit
@inject IStringLocalizer<SharedResources> localizer

@{
    var action = ViewData["Action"] as string;
    var area = ViewData["Area"] as string;
    var submitAction = area != null ? $"{action}?area={area}" : action;
    var vehicles = ViewData["Vehicles"] as List<SelectListItem>;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    string visible = action != "Create" ? "" : "hidden";
    string inputsVisible = action != "Create" || Model.Inputs.Count > 0 || Model.NoMaterialError ? "" : "hidden";
    string outPutVisible = Model.Status == MovementStatusEnum.Downloading || Model.Outputs.Count > 0 ? "" : "hidden";
    string checkboxVisible = Model.Status == MovementStatusEnum.Downloading || Model.Outputs.Count > 0 ? "" : "none";
}
@section ViewStyles{
    <link href="~/css/views/movementReport/createOrEdit.css" rel="stylesheet" />
}

<div asp-validation-summary="ModelOnly" class="text-danger"></div>

<form class="floating-labels" id="movementReportForm" method="POST" action="@submitAction">
    <input asp-for="Id" hidden />
    <div class="row">
        <div class="mr-2r" style="margin-left:1rem; min-width: 15rem;">
            @if (action == "Create")
            {
                <div id="vehicle-select">
                    <ignite-dropdown for-property="VehicleId"
                                     items="@vehicles"
                                     placeholder="@localizer[Lang.PlaceholderSelectVehicles]">
                    </ignite-dropdown>
                </div>
            }
            else
            {
                <div class="form-group m-t-20" binit-validation-for="@Model.VehicleId"
                     binit-onerror-class="has-danger" binit-onsuccess-class="has-success"
                     id="vehicle-select">
                    <select class="select2" id="VehicleId" name="VehicleId" disabled>
                        <option value="">@localizer[Lang.PlaceholderSelectVehicles]</option>
                        @foreach (var item in Model.Vehicles)
                        {
                            <option value="@item.Value" selected="@(item.Selected ? "selected" : null)">@item.Text</option>
                        }
                    </select>
                </div>
            }
        </div>
        @if (Model.Status == MovementStatusEnum.Loading && Model.Outputs.Count == 0)
        {
            <div style="align-self:center;margin-left:auto;margin-right:0.5rem" @visible>
                <button id="start-download" type="button" class="btn btn-secondary mr-2 mb-2">
                    @(localizer[Lang.BtnDownload])
                </button>
            </div>
        }
    </div>

    <div class="col-12 p-0">
        <div class="row" id="inputsContainer" @inputsVisible standard-row>
            <h4 style="margin-left:15px;">@localizer[Lang.InputsLabel]</h4>
            <table id="input-details" class="table w-100 details" style="margin-left:5px;">
                <thead id="inputs-header" @inputsVisible>
                    <tr>
                        <th>@(localizer[Lang.TableInputOrigin])</th>
                        <th>@(localizer[Lang.TableInputName])</th>
                        <th>@(localizer[Lang.TableInputQuantity])</th>
                        <th>@(localizer[Lang.TableInputCapacityUnit])</th>
                        <th>@(localizer[Lang.TableInputActions])</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int j = 0;
                        foreach (var inputs in Model.Inputs)
                        {
                            @await Component.InvokeAsync("InputMovementReportRow", new { index = j, model = Model, inputs = inputs })
                            j++;
                        }
                    }
                </tbody>
            </table>
            @if (Model.Status != MovementStatusEnum.Downloading && Model.Outputs.Count == 0)
            {
                <!-- Master Detail -->
                <button id="add-input" type="button" class="btn btn-primary mr-2 mb-2" style="margin-left: 15px;">
                    <i class="fa fa-plus"></i> @(localizer[Lang.BtnAddInput])
                </button>
            }
        </div>

    </div>

    <div id="outputsContainer" @outPutVisible>
        <div class="form-group col-12">
            <h4>@localizer[Lang.OutputsLabel]</h4>
            <table id="output-details" class="table w-100 details">
                <thead id="outputs-header" @outPutVisible>
                    <tr>
                        <th>@(localizer[Lang.TableInputDestination])</th>
                        <th>@(localizer[Lang.TableInputName])</th>
                        <th>@(localizer[Lang.TableInputQuantity])</th>
                        <th>@(localizer[Lang.TableInputCapacityUnit])</th>
                        <th>@(localizer[Lang.TableInputActions])</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int op = 0;
                        foreach (var outputs in Model.Outputs)
                        {
                            @await Component.InvokeAsync("OutputMovementReportRow", new { index = op, model = Model, outputs = outputs })
                            op++;
                        }
                    }
                </tbody>
            </table>
        </div>

        <!-- Master Detail -->
        <button id="add-output" type="button" class="btn btn-primary mr-2 mb-2">
            <i class="fa fa-plus"></i> @(localizer[Lang.BtnDownloadInput])
        </button>
    </div>

    <div class="d-flex justify-content-end" style="margin-right:2.5rem; max-height:5rem; display:@checkboxVisible !important" id="ready-checkbox">
        <ignite-checkbox for-property="@Model.Ready" size="large" color="#f07a22"></ignite-checkbox>
    </div>

    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index","MovementReport", new { area })'">
            @(localizer[Lang.BtnCancel])
        </button>
        <button type="button" onclick="validateSubmit()" class="btn btn-themecolor">@submitLabel</button>
    </div>
</form>


@section Scripts {
    <script>
        var movementReportCrLResources = @Json.Serialize(ViewData["MovementReportCrLResources"]);
        var area = '@area';
    </script>
    <script src="@Url.Content("~/js/views/movementReport/createoredit.js")" type="text/javascript"></script>
}

<ignite-load plugins="select2, switchery"></ignite-load>