@model WebApp.Models.HenBatchViewModel;
@using Binit.Framework;
@using Domain.Entities.Model;
@using Microsoft.Extensions.Localization;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenBatch.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer
@{
    var action = ViewData["Action"] as string;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var companies = ViewData["Companies"] as List<SelectListItem>;
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var clusters = ViewData["Clusters"] as List<SelectListItem>;
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
    var origins = ViewData["Origins"] as List<SelectListItem>;
    var henBatches = ViewData["HenBatches"] as List<SelectListItem>;
    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var actionsEnums = ViewData["ActionsEnums"] as List<SelectListItem>;
    var formulas = ViewData["ActiveFormulas"] as List<SelectListItem>;
    var categories = ViewData["ActiveHenBatchCategories"] as List<SelectListItem>;
    var eggMaterials = ViewData["EggMaterials"] as List<SelectListItem>;
    var userIsAdmin = (bool)ViewData["UserIsAdmin"];
    var formId = "henBatchForm";
    var units = ViewData["CapacityUnits"] as List<SelectListItem>;
    var hasClusters = (bool)ViewData["HasClusters"];
    var hasSectors = (bool)ViewData["HasSectors"];
}

@section ViewStyles{
    <link href="~/lib/bootstrap-datepicker/bootstrap-datepicker.min.css" rel="stylesheet" />
}

<div asp-validation-summary="ModelOnly" class="text-danger"></div>

<form class="floating-labels" id=@formId method="POST" action="@action">
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="ContainerProperties.Id" />
    <input type="hidden" asp-for="Name" />
    <input type="hidden" asp-for="HenStage" />
    <input type="hidden" asp-for="EggColor" />
    <input type="hidden" asp-for="HasTenantHenBatchCategory" />
    <input type="hidden" asp-for="HasReportsOrReceivedBirds" />
    <input type="hidden" asp-for="TenantAllowsToReceiveBirdsBeforeStartDate" />
    @if (action == "Edit")
    {
        <input type="hidden" asp-for="InitialAmountFemale" />
        <input type="hidden" asp-for="InitialAmountMale" />
    }

    <h3 class="card-title">@localizer[Lang.GeneralInformationTitle]</h3>
    <hr />
    <div class="row align-items-end">
        <div id="companyContainer"
             class="col-md-6 mt-5"
             style="@(companies.Count() > 1 ? "" : "display:none")">
            <ignite-dropdown for-property="CompanyId"
                             items="@companies"
                             placeholder="@localizer[Lang.PlaceholderSelectCompany]"
                             disabled="@(Model.HasReportsOrReceivedBirds)">
            </ignite-dropdown>
        </div>

        <div id="farmContainer" class="col-md-6 mt-5"
             style="@(farms.Count() > 1 ? "" : "display:none")">
            <ignite-dropdown for-property="ContainerProperties.FarmId"
                             items="@farms"
                             placeholder="@localizer[Lang.PlaceholderSelectFarm]"
                             disabled="@(Model.HasReportsOrReceivedBirds)">
            </ignite-dropdown>
        </div>

        <div class="col-md-6 mt-5">
            <ignite-input for-property="ContainerProperties.Code" disabled="@(Model.HasReportsOrReceivedBirds)"></ignite-input>
        </div>

        <div id="geneticContainer" class="col-md-6 mt-5">
            <ignite-dropdown for-property="GeneticId"
                             items="@genetics"
                             placeholder="@localizer[Lang.PlaceholderSelectGenetic]"
                             disabled="@(Model.HasReportsOrReceivedBirds)">
            </ignite-dropdown>
        </div>
        @if (!Model.TenantAllowsToReceiveBirdsBeforeStartDate)
        {
            <div class="col-md-6 mt-5">
                @if (Model.HenStage == HenStage.Laying)
                {
                    <label class="select">@localizer[Lang.DateStartLayingLabel]</label>
                }
                else
                {
                    <label class="select">@localizer[Lang.DateStartBreedingLabel]</label>
                }
                <div id="datePickerContainer" class="input-group m-b-40">
                    <input type="text" class="form-control" asp-for="DateStart" autocomplete="off" aria-describedby="calendar-addon" disabled="@(Model.HasReportsOrReceivedBirds)" />
                    <div class="input-group-append"><span class="input-group-text" id="calendar-addon"><i class="ti-calendar"></i></span></div>
                </div>
                <div>
                    <span asp-validation-for="DateStart" class="form-control-feedback"></span>
                </div>
            </div>
        }
        else
        {
            <div class="col-md-6 mt-5">
                <label class="select">@(Model.HenStage == HenStage.Laying ? localizer[Lang.OpeningDateLabelLaying] : localizer[Lang.OpeningDateLabelBreeding])</label>
                <div id="datePickerContainer" class="input-group m-b-40">
                    <input type="text" class="form-control" asp-for="OpeningDate" autocomplete="off" aria-describedby="calendar-addon" disabled="@(Model.HasReportsOrReceivedBirds)" />
                    <div class="input-group-append"><span class="input-group-text" id="calendar-addon"><i class="ti-calendar"></i></span></div>
                </div>
                <div>
                    <span asp-validation-for="OpeningDate" class="form-control-feedback"></span>
                </div>
            </div>
            <div class="col-md-6 mt-5">
                <label class="select">@(localizer[Lang.ReportingStartDateLabel])</label>
                <div id="reportingStartDateContainer" class="input-group m-b-40">
                    <input type="text" class="form-control" asp-for="ReportingStartDate" autocomplete="off" aria-describedby="calendar-addon" disabled="@(Model.HasReportsOrReceivedBirds)" />
                    <div class="input-group-append"><span class="input-group-text" id="calendar-addon"><i class="ti-calendar"></i></span></div>
                </div>
                <div>
                    <span asp-validation-for="ReportingStartDate" class="form-control-feedback"></span>
                </div>
            </div>
        }
        @if (Model.HenStage == HenStage.Laying)
        {
            <div class="col-md-6 mt-5">
                <label class="select">@localizer[Lang.StartDateOfWeekOneLabel]</label>
                <div id="sstartDateOfWeekOnePickerContainer" class="input-group m-b-40">
                    <input type="text" class="form-control" asp-for="StartDateOfWeekOne" autocomplete="off" aria-describedby="calendar-addon" disabled="@(Model.HasReportsOrReceivedBirds)" />
                    <div class="input-group-append"><span class="input-group-text" id="calendar-addon"><i class="ti-calendar"></i></span></div>
                </div>
                <div>
                    <span asp-validation-for="StartDateOfWeekOne" class="form-control-feedback"></span>
                </div>
            </div>
        }
        <div class="col-md-6 mt-5">
            <ignite-input for-property="BatchWeekNumber" disabled="@(Model.HasReportsOrReceivedBirds)"></ignite-input>
        </div>

        <div class="col-md-6 mt-5">
            <ignite-dropdown class="select2-material" for-property="FormulasIds"
                             items="formulas"
                             multiple="true">
            </ignite-dropdown>
        </div>

        <div class="col-md-6 mt-5">
            <ignite-textarea for-property="Description"></ignite-textarea>
        </div>

        @if (Model.HasTenantHenBatchCategory)
        {
            <div id="categoryContainer" class="col-md-6 mt-5">
                <ignite-dropdown for-property="CategoryId"
                                 items="@categories"
                                 placeholder="@localizer[Lang.PlaceholderSelectCategory]">
                </ignite-dropdown>
            </div>
        }

    </div>

    @if (action=="Edit" && Model.HasReportsOrReceivedBirds)
    {
        <div>
            <ignite-checkbox for-property="AllowBeginReportsOnAnyDate" color="#f07a22" disabled></ignite-checkbox>
        </div>
    }
    else
    {
        <div>
            <ignite-checkbox for-property="AllowBeginReportsOnAnyDate" color="#f07a22" ></ignite-checkbox>
        </div>
    }


    <h3 class="card-title">@(localizer[Lang.MaterialTypesTitle])</h3>
    <hr />

    <!-- Material type actions -->
    @await Component.InvokeAsync("MaterialTypeActionsTable", new { model = Model, materialTypes = materialTypes, actionsEnums = actionsEnums, units = units, editable = true})

    <h3 class="card-title">@(localizer[Lang.DistributionTitle])</h3>
    <hr />
    <!-- Henbatch location distribution -->
    @await Component.InvokeAsync("Distribution", new { henStage = Model.HenStage, model = Model.Distribution, henBatch = Guid.Parse(Model.Id), formId = formId, farm = Model.ContainerProperties.FarmId, action = action, hasClusters = hasClusters, hasHenBatchCategory = Model.HasTenantHenBatchCategory, categories = categories, editable = true })
    <span asp-validation-for="Distribution.Distributions" class="text-danger"></span>

    <!-- Aliases -->
    @await Component.InvokeAsync("EntityAlias", new { model = Model.AliasableViewModel, entityType = "HenBatch", entityId = Model.Id, formId = formId })

    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index", "HenBatch", new { henStage = Model.HenStage })'">
            @localizer[Lang.BtnCancel]
        </button>
        <button type="submit" class="btn btn-themecolor">@submitLabel</button>
    </div>
</form>

@section Scripts {
    <script>
        var hasBirds = '@Html.Raw(ViewData["HasBirds"])';
        var henStage = '@Model.HenStage';
        var henBatchCreateOrEditResources = @Json.Serialize(ViewData["HenBatchCreateOrEditResources"]);
        var disabledReason = '@Html.Raw(ViewData["DisabledReason"])';
        var entityType = '@Html.Raw(ViewData["EntityType"])';
        var userLanguage = "@(ViewData["UserLanguage"])";
        var allowToReceiveBirdsBeforeStartDate = '@Model.TenantAllowsToReceiveBirdsBeforeStartDate';
    </script>
    <script src="@Url.Content("~/lib/bootstrap-datepicker/bootstrap-datepicker.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/lib/bootstrap-datepicker/locales/bootstrap-datepicker.es.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/lib/bootstrap-datepicker/locales/bootstrap-datepicker.pt.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/containerSharedFunctions.js")" type="text/javascript"></script>
    <script src="~/js/distribution.js"></script>
    <script src="~/js/views/henBatch/createoredit.js"></script>
}

<ignite-load plugins="select2,autosize,date-time-picker,switchery"></ignite-load>